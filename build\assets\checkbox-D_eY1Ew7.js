import{j as i,o}from"./index-Cq3zfZON.js";import{u as N,c as L}from"./index-CGKXfO_7.js";import{c as O,P as R}from"./button-CTQLK12e.js";import{c as j}from"./index-Dc_FVRD7.js";import{u as H}from"./index-MsHvSdph.js";import{u as G}from"./index-DyHSLvub.js";import{u as K}from"./index-C2Z7v5r4.js";import{P as U}from"./index-VR2XJ8FE.js";import{C as X}from"./check-BfG1ovsW.js";var y="Checkbox",[$,ae]=O(y),[J,_]=$(y);function Q(t){const{__scopeCheckbox:n,checked:c,children:l,defaultChecked:s,disabled:e,form:f,name:b,onCheckedChange:d,required:m,value:k="on",internal_do_not_use_render:u}=t,[p,v]=H({prop:c,defaultProp:s??!1,onChange:d,caller:y}),[x,C]=i.useState(null),[g,r]=i.useState(null),a=i.useRef(!1),E=x?!!f||!!x.closest("form"):!0,P={checked:p,disabled:e,setChecked:v,control:x,setControl:C,name:b,form:f,value:k,hasConsumerStoppedPropagationRef:a,required:m,defaultChecked:h(s)?!1:s,isFormControl:E,bubbleInput:g,setBubbleInput:r};return o.jsx(J,{scope:n,...P,children:V(u)?u(P):l})}var S="CheckboxTrigger",w=i.forwardRef(({__scopeCheckbox:t,onKeyDown:n,onClick:c,...l},s)=>{const{control:e,value:f,disabled:b,checked:d,required:m,setControl:k,setChecked:u,hasConsumerStoppedPropagationRef:p,isFormControl:v,bubbleInput:x}=_(S,t),C=N(s,k),g=i.useRef(d);return i.useEffect(()=>{const r=e?.form;if(r){const a=()=>u(g.current);return r.addEventListener("reset",a),()=>r.removeEventListener("reset",a)}},[e,u]),o.jsx(R.button,{type:"button",role:"checkbox","aria-checked":h(d)?"mixed":d,"aria-required":m,"data-state":z(d),"data-disabled":b?"":void 0,disabled:b,value:f,...l,ref:C,onKeyDown:j(n,r=>{r.key==="Enter"&&r.preventDefault()}),onClick:j(c,r=>{u(a=>h(a)?!0:!a),x&&v&&(p.current=r.isPropagationStopped(),p.current||r.stopPropagation())})})});w.displayName=S;var B=i.forwardRef((t,n)=>{const{__scopeCheckbox:c,name:l,checked:s,defaultChecked:e,required:f,disabled:b,value:d,onCheckedChange:m,form:k,...u}=t;return o.jsx(Q,{__scopeCheckbox:c,checked:s,defaultChecked:e,disabled:b,required:f,onCheckedChange:m,name:l,form:k,value:d,internal_do_not_use_render:({isFormControl:p})=>o.jsxs(o.Fragment,{children:[o.jsx(w,{...u,ref:n,__scopeCheckbox:c}),p&&o.jsx(A,{__scopeCheckbox:c})]})})});B.displayName=y;var M="CheckboxIndicator",T=i.forwardRef((t,n)=>{const{__scopeCheckbox:c,forceMount:l,...s}=t,e=_(M,c);return o.jsx(U,{present:l||h(e.checked)||e.checked===!0,children:o.jsx(R.span,{"data-state":z(e.checked),"data-disabled":e.disabled?"":void 0,...s,ref:n,style:{pointerEvents:"none",...t.style}})})});T.displayName=M;var q="CheckboxBubbleInput",A=i.forwardRef(({__scopeCheckbox:t,...n},c)=>{const{control:l,hasConsumerStoppedPropagationRef:s,checked:e,defaultChecked:f,required:b,disabled:d,name:m,value:k,form:u,bubbleInput:p,setBubbleInput:v}=_(q,t),x=N(c,v),C=G(e),g=K(l);i.useEffect(()=>{const a=p;if(!a)return;const E=window.HTMLInputElement.prototype,I=Object.getOwnPropertyDescriptor(E,"checked").set,D=!s.current;if(C!==e&&I){const F=new Event("click",{bubbles:D});a.indeterminate=h(e),I.call(a,h(e)?!1:e),a.dispatchEvent(F)}},[p,C,e,s]);const r=i.useRef(h(e)?!1:e);return o.jsx(R.input,{type:"checkbox","aria-hidden":!0,defaultChecked:f??r.current,required:b,disabled:d,name:m,value:k,form:u,...n,tabIndex:-1,ref:x,style:{...n.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});A.displayName=q;function V(t){return typeof t=="function"}function h(t){return t==="indeterminate"}function z(t){return h(t)?"indeterminate":t?"checked":"unchecked"}function ce({className:t,...n}){return o.jsx(B,{"data-slot":"checkbox",className:L("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...n,children:o.jsx(T,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:o.jsx(X,{className:"size-3.5 text-background"})})})}export{ce as C};
