import{F as t,o as e}from"./index-Cq3zfZON.js";import{a as i}from"./index-CGKXfO_7.js";import{C as m,a as p}from"./card-B51fHEQ3.js";import{B as y}from"./button-CTQLK12e.js";import{P as g}from"./progress-BNQRkllt.js";import{D as w}from"./dynamic-banner-el9Dlhio.js";import{B as a}from"./badge-BWAn-Pqt.js";import{B as j}from"./badge-check-CQyl-k7O.js";/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f=[["line",{x1:"15",x2:"15",y1:"12",y2:"18",key:"1p7wdc"}],["line",{x1:"12",x2:"18",y1:"15",y2:"15",key:"1nscbv"}],["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],u=t("copy-plus",f);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N=[["line",{x1:"6",x2:"10",y1:"12",y2:"12",key:"161bw2"}],["line",{x1:"8",x2:"8",y1:"10",y2:"14",key:"1i6ji0"}],["line",{x1:"15",x2:"15.01",y1:"13",y2:"13",key:"dqpgro"}],["line",{x1:"18",x2:"18.01",y1:"11",y2:"11",key:"meh2c"}],["rect",{width:"20",height:"12",x:"2",y:"6",rx:"2",key:"9lu3g6"}]],k=t("gamepad",N);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v=[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]],b=t("network",v);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _=[["path",{d:"M16.466 7.5C15.643 4.237 13.952 2 12 2 9.239 2 7 6.477 7 12s2.239 10 5 10c.342 0 .677-.069 1-.2",key:"10n0gc"}],["path",{d:"m15.194 13.707 3.814 1.86-1.86 3.814",key:"16shm9"}],["path",{d:"M19 15.57c-1.804.885-4.274 1.43-7 1.43-5.523 0-10-2.239-10-5s4.477-5 10-5c4.838 0 8.873 1.718 9.8 4",key:"1lxi77"}]],B=t("rotate-3d",_);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M=[["path",{d:"M19.5 7a24 24 0 0 1 0 10",key:"8n60xe"}],["path",{d:"M4.5 7a24 24 0 0 0 0 10",key:"2lmadr"}],["path",{d:"M7 19.5a24 24 0 0 0 10 0",key:"1q94o2"}],["path",{d:"M7 4.5a24 24 0 0 1 10 0",key:"2z8ypa"}],["rect",{x:"17",y:"17",width:"5",height:"5",rx:"1",key:"1ac74s"}],["rect",{x:"17",y:"2",width:"5",height:"5",rx:"1",key:"1e7h5j"}],["rect",{x:"2",y:"17",width:"5",height:"5",rx:"1",key:"1t4eah"}],["rect",{x:"2",y:"2",width:"5",height:"5",rx:"1",key:"940dhs"}]],C=t("vector-square",M);function q({badgeItem:s}){return e.jsxs("div",{className:"flex md:flex-col md:space-y-1.5 space-x-1.5",children:[s?.version&&e.jsxs(a,{variant:"secondary",className:"bg-pink-400 text-white text-[10px] h-5 px-1.5 rounded-sm",children:[e.jsx(C,{className:"h-3 w-3 mr-0.5"}),s?.version]}),s?.isCrossVersion!==0&&e.jsxs(a,{variant:"secondary",className:"bg-pink-400 text-white text-[10px] h-5 px-1.5 rounded-sm",children:[e.jsx(B,{className:"h-3 w-3 mr-0.5"}),"跨版本"]}),s?.gamemodes&&e.jsxs(a,{variant:"secondary",className:"bg-pink-400 text-white text-[10px] h-5 px-1.5 rounded-sm",children:[e.jsx(k,{className:"h-3 w-3 mr-0.5"}),s.gamemodes]}),s?.isAuthMode!==0&&e.jsxs(a,{variant:"secondary",className:"bg-pink-400 text-white text-[10px] h-5 px-1.5 rounded-sm",children:[e.jsx(j,{className:"h-3 w-3 mr-0.5"}),"正版验证"]})]})}const z=({isActive:s,className:r})=>e.jsx("div",{className:i("flex items-center space-x-2",r),children:e.jsx("div",{className:`h-3 w-3 rounded-full ${s?"bg-green-500 shadow-green-glow animate-breathing":"bg-red-500 shadow-red-glow"}`})});function F({className:s,serverName:r,serverIconUrl:c,bannerUrl:n,addressUrl:d,maxPlayer:l,currentPlayer:x,serverBadge:o,isRuning:h=!1}){return e.jsx("div",{className:i(s),children:e.jsxs(m,{className:"py-2 rounded-sm max-w-3xl relative",children:[e.jsx(p,{className:"px-2",children:e.jsxs("div",{className:"flex flex-col md:flex-row text-center flex-1 min-w-[300px] md:space-x-1.5",children:[e.jsxs("div",{className:"w-full md:w-[70px]  order-1 md:order-none items-center flex md:flex-col md:space-y-1 p-1 space-x-1",children:[e.jsx("img",{src:c,alt:"Arclight Server Icon",className:"w-10 h-10 md:w-12 md:h-12 rounded-lg object-cover"}),e.jsx("p",{className:"text-sm  font-light line-clamp-2",children:r})]}),e.jsxs("div",{className:"flex-1 min-w-[150px] order-2 flex-row",children:[e.jsx("div",{className:"w-full",children:e.jsx(w,{url:n})}),e.jsx("div",{className:"",children:e.jsx(g,{className:"rounded-none bg-pink-200",value:33})}),e.jsxs("div",{className:"flex  items-center justify-between ",children:[e.jsxs("div",{className:"flex space-x-1",children:[e.jsx("div",{children:e.jsx("p",{className:"text-gray-800 font-light tracking-tight drop-shadow-sm line-clamp-1",children:d})}),e.jsx(y,{variant:"ghost",size:"icon",className:"w-5 h-5 cursor-pointer mt-1",children:e.jsx(u,{style:{width:14,height:14}})})]}),e.jsxs("div",{className:" inline-flex h-5 bg-pink-800 rounded-sm justify-center items-center p-0.5 flex-row space-x-0.5 mt-1",children:[e.jsx(b,{style:{width:12,height:12},color:"pink"}),e.jsxs("p",{style:{fontSize:11},className:"text-gray-100 whitespace-nowrap",children:[x,"/",l]})]})]})]}),e.jsx("div",{className:"w-full md:w-[70px] order-3",children:e.jsx(q,{badgeItem:o})})]})}),e.jsx(z,{isActive:h,className:"absolute top-2 right-2 md:left-1 md:top-1"})]})})}const Q="/assets/test_video_banner-Bq7n3QPF.webm",T="/assets/banner_img-6p_Ajcex.png",H="/assets/2b2t-DD4x0uE9.png",J="/assets/Arclight-B7M8NRQ6.jpg";var V=(s=>(s.Vanilla="Vanilla",s.Survival="生存服",s.NoRule="无规则",s))(V||{});export{H as B,F as S,V as a,J as b,T as c,Q as t};
