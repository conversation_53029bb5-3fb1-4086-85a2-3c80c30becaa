import{j as u,o as ne,m as he}from"./index-DTZYRUcV.js";import{u as X}from"./index-Csnngzkb.js";import{u as me}from"./index-Mx6w-wA8.js";import{P as ae}from"./button-D3EwPaXF.js";import{u as pe}from"./index-D2spak-M.js";function yt(e,t=globalThis?.document){const r=X(e);u.useEffect(()=>{const n=c=>{c.key==="Escape"&&r(c)};return t.addEventListener("keydown",n,{capture:!0}),()=>t.removeEventListener("keydown",n,{capture:!0})},[r,t])}var K="focusScope.autoFocusOnMount",U="focusScope.autoFocusOnUnmount",Z={bubbles:!1,cancelable:!0},ge="FocusScope",ye=u.forwardRef((e,t)=>{const{loop:r=!1,trapped:n=!1,onMountAutoFocus:c,onUnmountAutoFocus:i,...l}=e,[a,S]=u.useState(null),b=X(c),g=X(i),f=u.useRef(null),v=me(t,o=>S(o)),h=u.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;u.useEffect(()=>{if(n){let o=function(p){if(h.paused||!a)return;const y=p.target;a.contains(y)?f.current=y:k(f.current,{select:!0})},s=function(p){if(h.paused||!a)return;const y=p.relatedTarget;y!==null&&(a.contains(y)||k(f.current,{select:!0}))},d=function(p){if(document.activeElement===document.body)for(const E of p)E.removedNodes.length>0&&k(a)};document.addEventListener("focusin",o),document.addEventListener("focusout",s);const m=new MutationObserver(d);return a&&m.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",o),document.removeEventListener("focusout",s),m.disconnect()}}},[n,a,h.paused]),u.useEffect(()=>{if(a){Q.add(h);const o=document.activeElement;if(!a.contains(o)){const d=new CustomEvent(K,Z);a.addEventListener(K,b),a.dispatchEvent(d),d.defaultPrevented||(be(Re(oe(a)),{select:!0}),document.activeElement===o&&k(a))}return()=>{a.removeEventListener(K,b),setTimeout(()=>{const d=new CustomEvent(U,Z);a.addEventListener(U,g),a.dispatchEvent(d),d.defaultPrevented||k(o??document.body,{select:!0}),a.removeEventListener(U,g),Q.remove(h)},0)}}},[a,b,g,h]);const w=u.useCallback(o=>{if(!r&&!n||h.paused)return;const s=o.key==="Tab"&&!o.altKey&&!o.ctrlKey&&!o.metaKey,d=document.activeElement;if(s&&d){const m=o.currentTarget,[p,y]=Ee(m);p&&y?!o.shiftKey&&d===y?(o.preventDefault(),r&&k(p,{select:!0})):o.shiftKey&&d===p&&(o.preventDefault(),r&&k(y,{select:!0})):d===m&&o.preventDefault()}},[r,n,h.paused]);return ne.jsx(ae.div,{tabIndex:-1,...l,ref:v,onKeyDown:w})});ye.displayName=ge;function be(e,{select:t=!1}={}){const r=document.activeElement;for(const n of e)if(k(n,{select:t}),document.activeElement!==r)return}function Ee(e){const t=oe(e),r=G(t,e),n=G(t.reverse(),e);return[r,n]}function oe(e){const t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:n=>{const c=n.tagName==="INPUT"&&n.type==="hidden";return n.disabled||n.hidden||c?NodeFilter.FILTER_SKIP:n.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function G(e,t){for(const r of e)if(!Se(r,{upTo:t}))return r}function Se(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function we(e){return e instanceof HTMLInputElement&&"select"in e}function k(e,{select:t=!1}={}){if(e&&e.focus){const r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&we(e)&&t&&e.select()}}var Q=Ce();function Ce(){let e=[];return{add(t){const r=e[0];t!==r&&r?.pause(),e=$(e,t),e.unshift(t)},remove(t){e=$(e,t),e[0]?.resume()}}}function $(e,t){const r=[...e],n=r.indexOf(t);return n!==-1&&r.splice(n,1),r}function Re(e){return e.filter(t=>t.tagName!=="A")}var ke="Portal",Te=u.forwardRef((e,t)=>{const{container:r,...n}=e,[c,i]=u.useState(!1);pe(()=>i(!0),[]);const l=r||c&&globalThis?.document?.body;return l?he.createPortal(ne.jsx(ae.div,{...n,ref:t}),l):null});Te.displayName=ke;var C=function(){return C=Object.assign||function(t){for(var r,n=1,c=arguments.length;n<c;n++){r=arguments[n];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t},C.apply(this,arguments)};function ce(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,n=Object.getOwnPropertySymbols(e);c<n.length;c++)t.indexOf(n[c])<0&&Object.prototype.propertyIsEnumerable.call(e,n[c])&&(r[n[c]]=e[n[c]]);return r}function Pe(e,t,r){if(r||arguments.length===2)for(var n=0,c=t.length,i;n<c;n++)(i||!(n in t))&&(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))}var W="right-scroll-bar-position",B="width-before-scroll-bar",Ae="with-scroll-bars-hidden",Me="--removed-body-scroll-bar-size";function _(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Ne(e,t){var r=u.useState(function(){return{value:e,callback:t,facade:{get current(){return r.value},set current(n){var c=r.value;c!==n&&(r.value=n,r.callback(n,c))}}}})[0];return r.callback=t,r.facade}var Oe=typeof window<"u"?u.useLayoutEffect:u.useEffect,q=new WeakMap;function Le(e,t){var r=Ne(null,function(n){return e.forEach(function(c){return _(c,n)})});return Oe(function(){var n=q.get(r);if(n){var c=new Set(n),i=new Set(e),l=r.current;c.forEach(function(a){i.has(a)||_(a,null)}),i.forEach(function(a){c.has(a)||_(a,l)})}q.set(r,e)},[e]),r}function Fe(e){return e}function Ie(e,t){t===void 0&&(t=Fe);var r=[],n=!1,c={read:function(){if(n)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:e},useMedium:function(i){var l=t(i,n);return r.push(l),function(){r=r.filter(function(a){return a!==l})}},assignSyncMedium:function(i){for(n=!0;r.length;){var l=r;r=[],l.forEach(i)}r={push:function(a){return i(a)},filter:function(){return r}}},assignMedium:function(i){n=!0;var l=[];if(r.length){var a=r;r=[],a.forEach(i),l=r}var S=function(){var g=l;l=[],g.forEach(i)},b=function(){return Promise.resolve().then(S)};b(),r={push:function(g){l.push(g),b()},filter:function(g){return l=l.filter(g),r}}}};return c}function xe(e){e===void 0&&(e={});var t=Ie(null);return t.options=C({async:!0,ssr:!1},e),t}var ue=function(e){var t=e.sideCar,r=ce(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw new Error("Sidecar medium not found");return u.createElement(n,C({},r))};ue.isSideCarExport=!0;function We(e,t){return e.useMedium(t),ue}var ie=xe(),j=function(){},D=u.forwardRef(function(e,t){var r=u.useRef(null),n=u.useState({onScrollCapture:j,onWheelCapture:j,onTouchMoveCapture:j}),c=n[0],i=n[1],l=e.forwardProps,a=e.children,S=e.className,b=e.removeScrollBar,g=e.enabled,f=e.shards,v=e.sideCar,h=e.noRelative,w=e.noIsolation,o=e.inert,s=e.allowPinchZoom,d=e.as,m=d===void 0?"div":d,p=e.gapMode,y=ce(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=v,T=Le([r,t]),R=C(C({},y),c);return u.createElement(u.Fragment,null,g&&u.createElement(E,{sideCar:ie,removeScrollBar:b,shards:f,noRelative:h,noIsolation:w,inert:o,setCallbacks:i,allowPinchZoom:!!s,lockRef:r,gapMode:p}),l?u.cloneElement(u.Children.only(a),C(C({},R),{ref:T})):u.createElement(m,C({},R,{className:S,ref:T}),a))});D.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};D.classNames={fullWidth:B,zeroRight:W};var Be=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function De(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Be();return t&&e.setAttribute("nonce",t),e}function Ke(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Ue(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var _e=function(){var e=0,t=null;return{add:function(r){e==0&&(t=De())&&(Ke(t,r),Ue(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},je=function(){var e=_e();return function(t,r){u.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},le=function(){var e=je(),t=function(r){var n=r.styles,c=r.dynamic;return e(n,c),null};return t},He={left:0,top:0,right:0,gap:0},H=function(e){return parseInt(e||"",10)||0},Ve=function(e){var t=window.getComputedStyle(document.body),r=t[e==="padding"?"paddingLeft":"marginLeft"],n=t[e==="padding"?"paddingTop":"marginTop"],c=t[e==="padding"?"paddingRight":"marginRight"];return[H(r),H(n),H(c)]},Xe=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return He;var t=Ve(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},Ye=le(),N="data-scroll-locked",ze=function(e,t,r,n){var c=e.left,i=e.top,l=e.right,a=e.gap;return r===void 0&&(r="margin"),`
  .`.concat(Ae,` {
   overflow: hidden `).concat(n,`;
   padding-right: `).concat(a,"px ").concat(n,`;
  }
  body[`).concat(N,`] {
    overflow: hidden `).concat(n,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(n,";"),r==="margin"&&`
    padding-left: `.concat(c,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(l,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(n,`;
    `),r==="padding"&&"padding-right: ".concat(a,"px ").concat(n,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(W,` {
    right: `).concat(a,"px ").concat(n,`;
  }
  
  .`).concat(B,` {
    margin-right: `).concat(a,"px ").concat(n,`;
  }
  
  .`).concat(W," .").concat(W,` {
    right: 0 `).concat(n,`;
  }
  
  .`).concat(B," .").concat(B,` {
    margin-right: 0 `).concat(n,`;
  }
  
  body[`).concat(N,`] {
    `).concat(Me,": ").concat(a,`px;
  }
`)},J=function(){var e=parseInt(document.body.getAttribute(N)||"0",10);return isFinite(e)?e:0},Ze=function(){u.useEffect(function(){return document.body.setAttribute(N,(J()+1).toString()),function(){var e=J()-1;e<=0?document.body.removeAttribute(N):document.body.setAttribute(N,e.toString())}},[])},Ge=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,c=n===void 0?"margin":n;Ze();var i=u.useMemo(function(){return Xe(c)},[c]);return u.createElement(Ye,{styles:ze(i,!t,c,r?"":"!important")})},Y=!1;if(typeof window<"u")try{var L=Object.defineProperty({},"passive",{get:function(){return Y=!0,!0}});window.addEventListener("test",L,L),window.removeEventListener("test",L,L)}catch{Y=!1}var P=Y?{passive:!1}:!1,Qe=function(e){return e.tagName==="TEXTAREA"},se=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return r[t]!=="hidden"&&!(r.overflowY===r.overflowX&&!Qe(e)&&r[t]==="visible")},$e=function(e){return se(e,"overflowY")},qe=function(e){return se(e,"overflowX")},ee=function(e,t){var r=t.ownerDocument,n=t;do{typeof ShadowRoot<"u"&&n instanceof ShadowRoot&&(n=n.host);var c=fe(e,n);if(c){var i=de(e,n),l=i[1],a=i[2];if(l>a)return!0}n=n.parentNode}while(n&&n!==r.body);return!1},Je=function(e){var t=e.scrollTop,r=e.scrollHeight,n=e.clientHeight;return[t,r,n]},et=function(e){var t=e.scrollLeft,r=e.scrollWidth,n=e.clientWidth;return[t,r,n]},fe=function(e,t){return e==="v"?$e(t):qe(t)},de=function(e,t){return e==="v"?Je(t):et(t)},tt=function(e,t){return e==="h"&&t==="rtl"?-1:1},rt=function(e,t,r,n,c){var i=tt(e,window.getComputedStyle(t).direction),l=i*n,a=r.target,S=t.contains(a),b=!1,g=l>0,f=0,v=0;do{if(!a)break;var h=de(e,a),w=h[0],o=h[1],s=h[2],d=o-s-i*w;(w||d)&&fe(e,a)&&(f+=d,v+=w);var m=a.parentNode;a=m&&m.nodeType===Node.DOCUMENT_FRAGMENT_NODE?m.host:m}while(!S&&a!==document.body||S&&(t.contains(a)||t===a));return(g&&Math.abs(f)<1||!g&&Math.abs(v)<1)&&(b=!0),b},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},te=function(e){return[e.deltaX,e.deltaY]},re=function(e){return e&&"current"in e?e.current:e},nt=function(e,t){return e[0]===t[0]&&e[1]===t[1]},at=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},ot=0,A=[];function ct(e){var t=u.useRef([]),r=u.useRef([0,0]),n=u.useRef(),c=u.useState(ot++)[0],i=u.useState(le)[0],l=u.useRef(e);u.useEffect(function(){l.current=e},[e]),u.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(c));var o=Pe([e.lockRef.current],(e.shards||[]).map(re),!0).filter(Boolean);return o.forEach(function(s){return s.classList.add("allow-interactivity-".concat(c))}),function(){document.body.classList.remove("block-interactivity-".concat(c)),o.forEach(function(s){return s.classList.remove("allow-interactivity-".concat(c))})}}},[e.inert,e.lockRef.current,e.shards]);var a=u.useCallback(function(o,s){if("touches"in o&&o.touches.length===2||o.type==="wheel"&&o.ctrlKey)return!l.current.allowPinchZoom;var d=F(o),m=r.current,p="deltaX"in o?o.deltaX:m[0]-d[0],y="deltaY"in o?o.deltaY:m[1]-d[1],E,T=o.target,R=Math.abs(p)>Math.abs(y)?"h":"v";if("touches"in o&&R==="h"&&T.type==="range")return!1;var O=ee(R,T);if(!O)return!0;if(O?E=R:(E=R==="v"?"h":"v",O=ee(R,T)),!O)return!1;if(!n.current&&"changedTouches"in o&&(p||y)&&(n.current=E),!E)return!0;var z=n.current||E;return rt(z,s,o,z==="h"?p:y)},[]),S=u.useCallback(function(o){var s=o;if(!(!A.length||A[A.length-1]!==i)){var d="deltaY"in s?te(s):F(s),m=t.current.filter(function(E){return E.name===s.type&&(E.target===s.target||s.target===E.shadowParent)&&nt(E.delta,d)})[0];if(m&&m.should){s.cancelable&&s.preventDefault();return}if(!m){var p=(l.current.shards||[]).map(re).filter(Boolean).filter(function(E){return E.contains(s.target)}),y=p.length>0?a(s,p[0]):!l.current.noIsolation;y&&s.cancelable&&s.preventDefault()}}},[]),b=u.useCallback(function(o,s,d,m){var p={name:o,delta:s,target:d,should:m,shadowParent:ut(d)};t.current.push(p),setTimeout(function(){t.current=t.current.filter(function(y){return y!==p})},1)},[]),g=u.useCallback(function(o){r.current=F(o),n.current=void 0},[]),f=u.useCallback(function(o){b(o.type,te(o),o.target,a(o,e.lockRef.current))},[]),v=u.useCallback(function(o){b(o.type,F(o),o.target,a(o,e.lockRef.current))},[]);u.useEffect(function(){return A.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:v}),document.addEventListener("wheel",S,P),document.addEventListener("touchmove",S,P),document.addEventListener("touchstart",g,P),function(){A=A.filter(function(o){return o!==i}),document.removeEventListener("wheel",S,P),document.removeEventListener("touchmove",S,P),document.removeEventListener("touchstart",g,P)}},[]);var h=e.removeScrollBar,w=e.inert;return u.createElement(u.Fragment,null,w?u.createElement(i,{styles:at(c)}):null,h?u.createElement(Ge,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function ut(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const it=We(ie,ct);var lt=u.forwardRef(function(e,t){return u.createElement(D,C({},e,{ref:t,sideCar:it}))});lt.classNames=D.classNames;var st=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},M=new WeakMap,I=new WeakMap,x={},V=0,ve=function(e){return e&&(e.host||ve(e.parentNode))},ft=function(e,t){return t.map(function(r){if(e.contains(r))return r;var n=ve(r);return n&&e.contains(n)?n:(console.error("aria-hidden",r,"in not contained inside",e,". Doing nothing"),null)}).filter(function(r){return!!r})},dt=function(e,t,r,n){var c=ft(t,Array.isArray(e)?e:[e]);x[r]||(x[r]=new WeakMap);var i=x[r],l=[],a=new Set,S=new Set(c),b=function(f){!f||a.has(f)||(a.add(f),b(f.parentNode))};c.forEach(b);var g=function(f){!f||S.has(f)||Array.prototype.forEach.call(f.children,function(v){if(a.has(v))g(v);else try{var h=v.getAttribute(n),w=h!==null&&h!=="false",o=(M.get(v)||0)+1,s=(i.get(v)||0)+1;M.set(v,o),i.set(v,s),l.push(v),o===1&&w&&I.set(v,!0),s===1&&v.setAttribute(r,"true"),w||v.setAttribute(n,"true")}catch(d){console.error("aria-hidden: cannot operate on ",v,d)}})};return g(t),a.clear(),V++,function(){l.forEach(function(f){var v=M.get(f)-1,h=i.get(f)-1;M.set(f,v),i.set(f,h),v||(I.has(f)||f.removeAttribute(n),I.delete(f)),h||f.removeAttribute(r)}),V--,V||(M=new WeakMap,M=new WeakMap,I=new WeakMap,x={})}},bt=function(e,t,r){r===void 0&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),c=st(e);return c?(n.push.apply(n,Array.from(c.querySelectorAll("[aria-live], script"))),dt(n,c,r,"aria-hidden")):function(){return null}};export{ye as F,Te as P,lt as R,bt as h,yt as u};
