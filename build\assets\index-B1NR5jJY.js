function b(r){for(var n=[],e=0;e<r.length;){var t=r[e];if(t==="*"||t==="+"||t==="?"){n.push({type:"MODIFIER",index:e,value:r[e++]});continue}if(t==="\\"){n.push({type:"ESCAPED_CHAR",index:e++,value:r[e++]});continue}if(t==="{"){n.push({type:"OPEN",index:e,value:r[e++]});continue}if(t==="}"){n.push({type:"CLOSE",index:e,value:r[e++]});continue}if(t===":"){for(var v="",a=e+1;a<r.length;){var o=r.charCodeAt(a);if(o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122||o===95){v+=r[a++];continue}break}if(!v)throw new TypeError("Missing parameter name at ".concat(e));n.push({type:"NAME",index:e,value:v}),e=a;continue}if(t==="("){var u=1,h="",a=e+1;if(r[a]==="?")throw new TypeError('Pattern cannot start with "?" at '.concat(a));for(;a<r.length;){if(r[a]==="\\"){h+=r[a++]+r[a++];continue}if(r[a]===")"){if(u--,u===0){a++;break}}else if(r[a]==="("&&(u++,r[a+1]!=="?"))throw new TypeError("Capturing groups are not allowed at ".concat(a));h+=r[a++]}if(u)throw new TypeError("Unbalanced pattern at ".concat(e));if(!h)throw new TypeError("Missing pattern at ".concat(e));n.push({type:"PATTERN",index:e,value:h}),e=a;continue}n.push({type:"CHAR",index:e,value:r[e++]})}return n.push({type:"END",index:e,value:""}),n}function H(r,n){n===void 0&&(n={});for(var e=b(r),t=n.prefixes,v=t===void 0?"./":t,a=n.delimiter,o=a===void 0?"/#?":a,u=[],h=0,E=0,p="",f=function(l){if(E<e.length&&e[E].type===l)return e[E++].value},O=function(l){var c=f(l);if(c!==void 0)return c;var g=e[E],M=g.type,S=g.index;throw new TypeError("Unexpected ".concat(M," at ").concat(S,", expected ").concat(l))},C=function(){for(var l="",c;c=f("CHAR")||f("ESCAPED_CHAR");)l+=c;return l},P=function(l){for(var c=0,g=o;c<g.length;c++){var M=g[c];if(l.indexOf(M)>-1)return!0}return!1},y=function(l){var c=u[u.length-1],g=l||(c&&typeof c=="string"?c:"");if(c&&!g)throw new TypeError('Must have text between two parameters, missing text after "'.concat(c.name,'"'));return!g||P(g)?"[^".concat(w(o),"]+?"):"(?:(?!".concat(w(g),")[^").concat(w(o),"])+?")};E<e.length;){var m=f("CHAR"),d=f("NAME"),T=f("PATTERN");if(d||T){var x=m||"";v.indexOf(x)===-1&&(p+=x,x=""),p&&(u.push(p),p=""),u.push({name:d||h++,prefix:x,suffix:"",pattern:T||y(x),modifier:f("MODIFIER")||""});continue}var i=m||f("ESCAPED_CHAR");if(i){p+=i;continue}p&&(u.push(p),p="");var R=f("OPEN");if(R){var x=C(),s=f("NAME")||"",N=f("PATTERN")||"",A=C();O("CLOSE"),u.push({name:s||(N?h++:""),pattern:s&&!N?y(x):N,prefix:x,suffix:A,modifier:f("MODIFIER")||""});continue}O("END")}return u}function w(r){return r.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function D(r){return r&&r.sensitive?"":"i"}function I(r,n){if(!n)return r;for(var e=/\((?:\?<(.*?)>)?(?!\?)/g,t=0,v=e.exec(r.source);v;)n.push({name:v[1]||t++,prefix:"",suffix:"",modifier:"",pattern:""}),v=e.exec(r.source);return r}function W(r,n,e){var t=r.map(function(v){return _(v,n,e).source});return new RegExp("(?:".concat(t.join("|"),")"),D(e))}function $(r,n,e){return F(H(r,e),n,e)}function F(r,n,e){e===void 0&&(e={});for(var t=e.strict,v=t===void 0?!1:t,a=e.start,o=a===void 0?!0:a,u=e.end,h=u===void 0?!0:u,E=e.encode,p=E===void 0?function(c){return c}:E,f=e.delimiter,O=f===void 0?"/#?":f,C=e.endsWith,P=C===void 0?"":C,y="[".concat(w(P),"]|$"),m="[".concat(w(O),"]"),d=o?"^":"",T=0,x=r;T<x.length;T++){var i=x[T];if(typeof i=="string")d+=w(p(i));else{var R=w(p(i.prefix)),s=w(p(i.suffix));if(i.pattern)if(n&&n.push(i),R||s)if(i.modifier==="+"||i.modifier==="*"){var N=i.modifier==="*"?"?":"";d+="(?:".concat(R,"((?:").concat(i.pattern,")(?:").concat(s).concat(R,"(?:").concat(i.pattern,"))*)").concat(s,")").concat(N)}else d+="(?:".concat(R,"(").concat(i.pattern,")").concat(s,")").concat(i.modifier);else{if(i.modifier==="+"||i.modifier==="*")throw new TypeError('Can not repeat "'.concat(i.name,'" without a prefix and suffix'));d+="(".concat(i.pattern,")").concat(i.modifier)}else d+="(?:".concat(R).concat(s,")").concat(i.modifier)}}if(h)v||(d+="".concat(m,"?")),d+=e.endsWith?"(?=".concat(y,")"):"$";else{var A=r[r.length-1],l=typeof A=="string"?m.indexOf(A[A.length-1])>-1:A===void 0;v||(d+="(?:".concat(m,"(?=").concat(y,"))?")),l||(d+="(?=".concat(m,"|").concat(y,")"))}return new RegExp(d,D(e))}function _(r,n,e){return r instanceof RegExp?I(r,n):Array.isArray(r)?W(r,n,e):$(r,n,e)}export{H as parse,_ as pathToRegexp,F as tokensToRegexp};
