import{j as b,o as r}from"./index-Cq3zfZON.js";import{B as l}from"./button-CTQLK12e.js";import{B as v}from"./badge-BWAn-Pqt.js";import{I as o}from"./icon-DWEEtME9.js";import{S as j}from"./scroll-area-DSIcPaiT.js";import{a as g,S as y}from"./use-system-DaYsginW.js";import{u as w}from"./use-tabs-D9RsThBN.js";import{a as n}from"./index-CGKXfO_7.js";function S(){const{savedTabs:t,removeTab:i,selectedTab:c,setSelectedTab:m,navigateToTab:d}=w(),{showHeaderTab:u}=g(),a=b.useRef(null),h=()=>{if(a.current){const e=a.current.querySelector("[data-radix-scroll-area-viewport]");e&&e.scrollBy({left:-100,behavior:"smooth"})}},p=()=>{if(a.current){const e=a.current.querySelector("[data-radix-scroll-area-viewport]");e&&e.scrollBy({left:100,behavior:"smooth"})}},f=(e,s)=>{e.stopPropagation(),i(s)},x=e=>{m(e),d(e)};return r.jsxs("div",{className:n("justify-between items-center gap-0 w-full z-10 px-1 transition-all ease-in-out duration-300 flex",t.length<1||!u?"opacity-0 h-0":"opacity-100 h-8"),children:[r.jsx(l,{variant:"ghost",className:"w-3 h-6 cursor-pointer",onClick:h,children:r.jsx(o,{icon:"line-md:chevron-small-left",size:23})}),r.jsx("div",{className:"flex-1 min-w-0 ",children:r.jsx(j,{ref:a,className:"w-full",children:r.jsx("div",{className:"flex whitespace-nowrap",children:t.map(e=>r.jsxs(r.Fragment,{children:[r.jsxs(v,{onClick:()=>x(e.meta.key),variant:"secondary",className:n("mr-0 flex-shrink-0 min-w-8 min-h-7 rounded-xs cursor-pointer transition-all ease-in-out duration-100",c===e.meta.key&&"bg-primary text-white"),children:[e.meta.label,r.jsx("div",{onClick:s=>f(s,e.meta.key),className:"cursor-pointer hover:bg-gray-300 transition-all ease-in-out duration-100 rounded-sm ml-1 p-0.5",children:r.jsx(o,{icon:"line-md:close",size:18})})]},e.meta.key),r.jsx(y,{orientation:"vertical",className:"mr-1"})]}))})})}),r.jsx(l,{variant:"ghost",className:"w-3 h-6 cursor-pointer",onClick:p,children:r.jsx(o,{icon:"line-md:chevron-small-right",size:23})})]})}const R=Object.freeze(Object.defineProperty({__proto__:null,default:S},Symbol.toStringTag,{value:"Module"}));export{S,R as _};
