import{j as r,B as m}from"./index-DTZYRUcV.js";import{u as v}from"./index-D2spak-M.js";var R=m[" useInsertionEffect ".trim().toString()]||v;function b({prop:e,defaultProp:s,onChange:t=()=>{},caller:f}){const[u,c,i]=C({defaultProp:s,onChange:t}),o=e!==void 0,a=o?e:u;{const n=r.useRef(e!==void 0);r.useEffect(()=>{const l=n.current;l!==o&&console.warn(`${f} is changing from ${l?"controlled":"uncontrolled"} to ${o?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),n.current=o},[o,f])}const d=r.useCallback(n=>{if(o){const l=E(n)?n(e):n;l!==e&&i.current?.(l)}else c(n)},[o,e,c,i]);return[a,d]}function C({defaultProp:e,onChange:s}){const[t,f]=r.useState(e),u=r.useRef(t),c=r.useRef(s);return R(()=>{c.current=s},[s]),r.useEffect(()=>{u.current!==t&&(c.current?.(t),u.current=t)},[t,u]),[t,f,c]}function E(e){return typeof e=="function"}export{b as u};
