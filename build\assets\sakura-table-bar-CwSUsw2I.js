import{o as e}from"./index-DTZYRUcV.js";import{I as f}from"./input-BCM2zmLI.js";import{B as a}from"./button-D3EwPaXF.js";function j({enableSearch:r=!1,enableSelected:t=!1,enableCreate:l=!1,searchPlaceholder:n,searchValue:i,createButtonText:c,selectCount:s,onOpenCreateDialog:m,onSearchChange:o,onDeleteItems:x}){return e.jsxs("div",{className:"flex py-4 space-x-1.5 items-center",children:[r&&e.jsx(f,{placeholder:n,value:i,onChange:d=>o(d.target.value),className:"max-w-sm"}),l&&e.jsx(a,{variant:"default",className:"text-background",onClick:m,children:c}),t&&s>0&&e.jsxs("div",{className:"flex justify-center items-center gap-3 md:flex-row-reverse",children:[e.jsxs("div",{className:"text-muted-foreground  text-sm hidden lg:block",children:[s," 行 被选中了"]}),e.jsx(a,{onClick:()=>{x()},children:"删除选中"})]})]})}export{j as S};
