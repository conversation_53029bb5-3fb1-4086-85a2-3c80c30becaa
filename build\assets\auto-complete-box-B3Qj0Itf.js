import{_ as R}from"./index-DHgkpITs.js";import{_ as G}from"./index-Br13FYP8.js";import{u as H,a as z,b as A,c as Z,d as Y,_ as $}from"./permissiom-select-tree-Cl0QbKpR.js";import{_ as q}from"./server-card-playground-DTk74m2x.js";import{_ as J}from"./border-loading-playground-BWPCglbk.js";import{_ as K}from"./index-Bmu1OsnQ.js";import{_ as Q}from"./index-Cnfcm1pE.js";import{_ as W}from"./index-Cq0CNt15.js";import{_ as X}from"./index-DposQIAA.js";import{_ as ee}from"./system-setting-CiG_uTmh.js";import{_ as se}from"./system-tabs-BzLLNYwK.js";import{_ as re}from"./index-DWZj3pe3.js";import{_ as oe}from"./index-kHClkcy_.js";import{_ as ae}from"./login-form-BmTfNo0O.js";import{_ as te}from"./index-CPz5HxPV.js";import{U as ne,_ as ie}from"./column-BKAee9-t.js";import{S as le,P as ce,a as me,b as de,C as _e,c as xe,d as ge,e as pe,f as ue,g as he,_ as je}from"./select-parent-box-v5F77yRj.js";import{j as y,o as e}from"./index-DTZYRUcV.js";import{B as C}from"./button-D3EwPaXF.js";import{D as be,a as fe,b as ve,c as Ne,d as ye,e as Ce,f as Se}from"./dialog-BNATxwkx.js";import{I as h}from"./input-BCM2zmLI.js";import{_ as Pe,Z as De,u as Te,a as Oe,F as Me,b as c,c as m,d,e as _,f as x,s as g,o as Ie,n as D}from"./form-_T1chqIj.js";import{t as T}from"./index-7CsvrnxQ.js";import{T as L,a as N,_ as Le}from"./dialog-BlQMfYTP.js";import{S as Fe}from"./index-kd47zalI.js";import{S as Ee}from"./sakura-table-bar-CwSUsw2I.js";import{_ as we}from"./colums-CPNPr5k6.js";import{_ as Ue}from"./index-Dh4izFyC.js";import{_ as ke}from"./index-BEWhpUmI.js";import{_ as Ve}from"./columns-CCzzK9iz.js";import{_ as Be}from"./index-BqZG0FKz.js";import{_ as Re}from"./columns-Bz_mHAFD.js";import{_ as Ge}from"./index-wnxAA7CV.js";import{_ as He}from"./index-Dj2PlnGy.js";import{_ as ze}from"./columns-CHxP5HKd.js";import{_ as Ae}from"./index-CXeJAErD.js";import{_ as Ze}from"./test-icon-select-panel-DV0R-2np.js";import{_ as Ye}from"./index-Uo7FA7px.js";import{_ as $e}from"./index-BiA4dpup.js";import{_ as qe}from"./colums-BzRZFxMk.js";import{_ as Je}from"./dialog-Bdb99DKu.js";import{_ as Ke}from"./index-7zrbc0Ii.js";import{_ as Qe}from"./index-oCEm0Sy1.js";import{c as We}from"./index-Mx6w-wA8.js";import{C as Xe}from"./chevrons-up-down-Bd2n7KTP.js";import{C as es}from"./check-BUEKYezN.js";function ss(t){return Pe(De,t)}const rs=Ie({id:g().min(2,{message:"ID最短需要2个字符"}),name:g().min(2,{message:"用户名最短需要2个字符"}),label:g().min(2,{message:"标签最短需要2个字符"}),type:D().min(0).max(1),route:g().min(2,{message:"标签最短需要2个字符"}),status:D().min(0).max(1),order:ss().int(),icon:g().min(2,{message:"标签最短需要2个字符"}),component:g().min(0,{message:"标签最短需要0个字符"}),parentId:g().min(0,{message:"父亲ID最短需要0个字符"}),hide:D().min(0).max(1)});function E({open:t,onClose:i,updateItem:o,handleCreate:p,handleEdit:n,type:l}){const[u,j]=y.useState("0"),[S,b]=y.useState("1"),{data:f,isLoading:P}=H(),{data:v,isLoading:U,setParentId:O}=z(),k=A(),M=()=>rs,r=Te({resolver:Oe(M()),defaultValues:{id:"",parentId:"",name:"",label:"",type:0,route:"",status:1,order:0,icon:"",component:"",hide:0}});y.useEffect(()=>{o?(r.reset({id:o.id||"",parentId:o.parentId||"",name:o.name||"",label:o.label||"",type:o.type||0,route:o.route||"",status:o.status||1,order:o.order||0,icon:o.icon||"",component:o.component||"",hide:o.hide?1:0}),j(String(o.type||0)),b(String(o.status||1))):(r.reset({id:"",parentId:"",name:"",label:"",type:0,route:"",status:1,order:0,icon:"",component:"",hide:0}),j("0"),b("1"))},[o,r]);const V=s=>{console.log(s);let a=M();Number(s.type)===1&&(a=a.extend({parentId:g().min(1,{message:"请选择父节点"}),component:g().min(1,{message:"请选择组件"})}));const I=a.safeParse(s);if(I.success)l=="update"?(n(s),console.log("update permission")):(p(s),console.log("create permission")),i?.();else{if(Number(s.type)===1&&!s.parentId){T.error("在视图模式下，请先选择父节点！");return}T.error("表单提交失败，请检查字段！"),console.log("验证错误:",I.error.issues)}},B=()=>{if(Number(u)===0)!P&&f&&r.setValue("id",f.data.id);else{if(!r.getValues().parentId){T.error("在视图模式下，请先选择父节点！");return}O(r.getValues().parentId),!U&&v&&r.setValue("id",v.data.id)}};return e.jsx(be,{open:t,onOpenChange:i,children:e.jsxs(fe,{className:"sm:max-w-[800px]",children:[e.jsxs(ve,{children:[e.jsx(Ne,{className:"text-foreground",children:l!=="create"?"编辑权限节点":"新增权限节点"}),e.jsx(ye,{className:"text-foreground",children:l!=="create"?"编辑现有的权限节点":"构建新权限节点的相关信息"})]}),e.jsx(Me,{...r,children:e.jsxs("form",{id:"create-user-form",onSubmit:r.handleSubmit(V),className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(c,{control:r.control,name:"type",render:({field:s})=>e.jsxs(m,{children:[e.jsx(d,{className:"text-foreground",children:"节点类型"}),e.jsx(_,{children:e.jsxs(L,{variant:"outline",type:"single",value:u,onValueChange:a=>{j(a),s.onChange(Number(a)),r.reset({...r.getValues(),parentId:"",component:""})},children:[e.jsx(N,{value:"0",children:e.jsx("p",{className:"italic text-xs ",children:"菜单"})}),e.jsx(N,{value:"1",children:e.jsx("p",{className:"italic text-xs ",children:"视图"})})]})}),e.jsx(x,{})]})}),e.jsx(c,{control:r.control,name:"status",render:({field:s})=>e.jsxs(m,{children:[e.jsx(d,{className:"text-foreground",children:"状态"}),e.jsx(_,{children:e.jsxs(L,{variant:"outline",type:"single",value:S,onValueChange:a=>{b(a),s.onChange(Number(a))},children:[e.jsx(N,{value:"1",children:e.jsx("p",{className:"italic text-xs ",children:"开启"})}),e.jsx(N,{value:"0",children:e.jsx("p",{className:"italic text-xs ",children:"禁用"})})]})}),e.jsx(x,{})]})})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[Number(u)===1&&e.jsx(c,{control:r.control,name:"parentId",render:({field:s})=>e.jsxs(m,{children:[e.jsx(d,{className:"text-foreground",children:"父节点"}),e.jsx(_,{children:e.jsx(le,{list:k.data.data,value:s.value,onChange:a=>{s.onChange(a),O(a)}})}),e.jsx(x,{})]})}),Number(u)===1&&e.jsx(c,{control:r.control,name:"component",render:({field:s})=>e.jsxs(m,{children:[e.jsx(d,{className:"text-foreground",children:"组件"}),e.jsx(_,{children:e.jsx(w,{value:s.value,onChange:s.onChange})}),e.jsx(x,{})]})})]}),e.jsx(c,{control:r.control,name:"id",render:({field:s})=>e.jsxs(m,{children:[e.jsx(d,{className:"text-foreground",children:"ID"}),e.jsx(_,{children:e.jsxs("div",{className:"flex",children:[e.jsx(h,{placeholder:"输入唯一id",className:"rounded-r-none focus-visible:ring-0",...s}),e.jsx(C,{type:"button",className:"rounded-l-none w-fit p-1 bg-primary  hover:bg-primary",size:"icon",onClick:B,children:e.jsx("span",{className:"italic font-bold text-xs",children:"获取"})})]})}),e.jsx(x,{})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(c,{control:r.control,name:"label",render:({field:s})=>e.jsxs(m,{children:[e.jsx(d,{className:"text-foreground",children:"渲染标签"}),e.jsx(_,{children:e.jsx(h,{type:"text",placeholder:"用于浏览器显示",className:"placeholder:text-sm focus-visible:ring-primary",...s})}),e.jsx(x,{})]})}),e.jsx(c,{control:r.control,name:"name",render:({field:s})=>e.jsxs(m,{children:[e.jsx(d,{className:"text-foreground",children:"名称"}),e.jsx(_,{children:e.jsx(h,{type:"text",placeholder:"输入权限点名称",className:"placeholder:text-sm focus-visible:ring-primary",...s})}),e.jsx(x,{})]})})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(c,{control:r.control,name:"route",render:({field:s})=>e.jsxs(m,{children:[e.jsx(d,{className:"text-foreground",children:"路由切片"}),e.jsx(_,{children:e.jsx(h,{type:"text",placeholder:"输入此节点挂载的路由片段",className:"placeholder:text-sm focus-visible:ring-primary",...s})}),e.jsx(x,{})]})}),e.jsx(c,{control:r.control,name:"icon",render:({field:s})=>e.jsxs(m,{children:[e.jsx(d,{className:"text-foreground",children:"图标"}),e.jsx(_,{children:e.jsx(h,{type:"text",placeholder:"iconfiy 图标name",className:"placeholder:text-sm focus-visible:ring-primary",...s})}),e.jsx(x,{})]})})]}),e.jsx(c,{control:r.control,name:"order",render:({field:s})=>e.jsxs(m,{children:[e.jsx(d,{className:"text-foreground",children:"顺序"}),e.jsx(_,{children:e.jsx(h,{type:"number",placeholder:"排序顺序",className:"placeholder:text-sm focus-visible:ring-primary",...s,value:String(s.value),onChange:a=>s.onChange(a.target.value)})}),e.jsx(x,{})]})})]})}),e.jsxs(Ce,{children:[e.jsx(Se,{asChild:!0,children:e.jsx(C,{variant:"outline",onClick:i,children:"取消"})}),e.jsx(C,{className:" bg-primary  hover:bg-primary ",type:"submit",form:"create-user-form",children:l==="create"?"新增":l==="update"?"更新":"追加"})]})]})})}const os=Object.freeze(Object.defineProperty({__proto__:null,default:E},Symbol.toStringTag,{value:"Module"}));function as(){const{data:t}=Z(),{handleCreatePermission:i,handleDeletePermission:o,handleEditPermission:p,handleOpenEditDialog:n,handleCloseDialog:l,handleOpenCreateDialog:u,handleOpenPushDialog:j,isCreateDialogOpen:S,isEditDialogOpen:b,editingItem:f,dialogType:P}=Y(),v=ne({handleOpenEditDialog:n,handleDeletePermission:o,handleOpenPushDialog:j});return e.jsxs("div",{className:"container mx-auto",children:[e.jsx(Ee,{enableSearch:!1,enableCreate:!0,enableSelected:!1,onOpenCreateDialog:u,searchPlaceholder:"搜索名称",createButtonText:"创建权限节点"}),e.jsx(Fe,{columns:v,data:t}),e.jsx(E,{open:S||b,enableSelected:!0,updateItem:f,handleCreate:i,handleEdit:p,onClose:l,type:P})]})}const ts=Object.freeze(Object.defineProperty({__proto__:null,default:as},Symbol.toStringTag,{value:"Module"})),ns="/src/pages",is=Object.assign({"/src/pages/Article/index.tsx":R,"/src/pages/Component/index.tsx":G,"/src/pages/Component/permissiom-select-tree.tsx":$,"/src/pages/Component/server-card-playground.tsx":q,"/src/pages/CssPlayground/border-loading-playground.tsx":J,"/src/pages/CssPlayground/index.tsx":K,"/src/pages/Hero/about-project/index.tsx":Q,"/src/pages/Hero/index.tsx":W,"/src/pages/Home/index.tsx":X,"/src/pages/Layout/components/system-setting.tsx":ee,"/src/pages/Layout/components/system-tabs.tsx":se,"/src/pages/Layout/index.tsx":re,"/src/pages/Login/index.tsx":oe,"/src/pages/Login/login-form.tsx":ae,"/src/pages/PermissionManagement/index.tsx":te,"/src/pages/PermissionManagement/user-permission/column.tsx":ie,"/src/pages/PermissionManagement/user-permission/components/select-parent-box.tsx":je,"/src/pages/PermissionManagement/user-permission/dialog.tsx":os,"/src/pages/PermissionManagement/user-permission/index.tsx":ts,"/src/pages/PermissionManagement/user-role/colums.tsx":we,"/src/pages/PermissionManagement/user-role/dialog.tsx":Le,"/src/pages/PermissionManagement/user-role/index.tsx":Ue,"/src/pages/Publish/index.tsx":ke,"/src/pages/Server/achrive-list/columns.tsx":Ve,"/src/pages/Server/achrive-list/index.tsx":Be,"/src/pages/Server/baned-list/columns.tsx":Re,"/src/pages/Server/baned-list/index.tsx":Ge,"/src/pages/Server/index.tsx":He,"/src/pages/Server/review-list/columns.tsx":ze,"/src/pages/Server/review-list/index.tsx":Ae,"/src/pages/Test/test-icon-select-panel.tsx":Ze,"/src/pages/Tools/api-tool/index.tsx":Ye,"/src/pages/Tools/index.tsx":$e,"/src/pages/UserManagement/admin-user-management/colums.tsx":qe,"/src/pages/UserManagement/admin-user-management/dialog.tsx":Je,"/src/pages/UserManagement/admin-user-management/index.tsx":Ke,"/src/pages/UserManagement/index.tsx":Qe}),F=Object.keys(is).map(t=>{const i=t.replace(ns,"");return{label:i,value:i}});function w({value:t,onChange:i}){const[o,p]=y.useState(!1);return e.jsxs(ce,{open:o,onOpenChange:p,modal:!1,children:[e.jsx(me,{asChild:!0,children:e.jsxs(C,{variant:"outline",role:"combobox","aria-expanded":o,className:"w-full justify-between",children:[e.jsx("span",{className:"truncate w-[200px] lg:w-[300px]",children:t?F.find(n=>n.value===t)?.label:"选择组件"}),e.jsx(Xe,{className:"opacity-50"})]})}),e.jsx(de,{className:"p-0",style:{pointerEvents:"auto"},children:e.jsxs(_e,{children:[e.jsx(xe,{placeholder:"搜索节点",className:"h-9"}),e.jsxs(ge,{children:[e.jsx(pe,{children:"没有任何父节点"}),e.jsx(ue,{children:F.map(n=>e.jsxs(he,{value:n.value,keywords:[n.label],onSelect:l=>{i(l===t?"":l),p(!1)},children:[n.label,e.jsx(es,{className:We("ml-auto",t===n.value?"opacity-100":"opacity-0")})]},n.value))})]})]})})]})}const er=Object.freeze(Object.defineProperty({__proto__:null,PageAutocomplete:w},Symbol.toStringTag,{value:"Module"}));export{os as _,ts as a,er as b};
