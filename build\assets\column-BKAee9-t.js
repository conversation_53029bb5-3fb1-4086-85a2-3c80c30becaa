import{F as x,o as e}from"./index-DTZYRUcV.js";import{P as o}from"./index-7CsvrnxQ.js";import{B as l}from"./button-D3EwPaXF.js";import{B as n}from"./badge-DuGpzd0C.js";import{D as c,a as i,b as d,c as t,d as p}from"./dropdown-menu-BZRMPeHZ.js";import{I as j}from"./icon-EoRqrusI.js";import{a as b}from"./index-Mx6w-wA8.js";import{c as f}from"./index-CzvqTnBS.js";import{P as N}from"./plus-B5Z3e102.js";import{E as y}from"./ellipsis-k69hbTZf.js";import{M as C}from"./move-down-DGE8A7gH.js";/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v=[["path",{d:"M5 12h14",key:"1ays0h"}]],_=x("minus",v),a=f();function k({handleOpenEditDialog:m,handleDeletePermission:u,handleOpenPushDialog:g}){return[a.accessor("name",{header:"名称",cell:({row:r})=>e.jsxs("div",{className:"flex items-center gap-1.5",children:[r.getCanExpand()?e.jsx(l,{variant:"secondary",className:"bg-foreground w-5 h-5 hover:border-1 cursor-pointer hover:bg-primary",onClick:r.getToggleExpandedHandler(),children:r.getIsExpanded()?e.jsx(_,{className:"text-background"}):e.jsx(N,{className:"text-background"})}):null,r.getValue("name")]})}),a.accessor("label",{header:"国际化标签"}),a.accessor("icon",{header:"图标",cell:r=>r.getValue()?e.jsx(j,{icon:r.getValue(),size:18}):null}),a.accessor("type",{header:"类型",cell:r=>r.getValue()?e.jsx(n,{className:"bg-primary",children:"界面"}):e.jsx(n,{className:"bg-primary",children:"种类"})}),a.accessor("status",{header:"状态",cell:r=>{const s=r.getValue();return e.jsx(n,{className:b((s===o.DISABLE,"bg-primary"),"text-background"),children:s===o.DISABLE?"禁用":"启用"})}}),a.accessor("order",{header:"顺序",cell:r=>r.getValue()&&e.jsx(n,{variant:"secondary",children:r.getValue()})}),a.accessor("route",{header:"路由",cell:r=>{const s=r.getValue();return s?e.jsx("span",{className:"italic text-[12px]",children:s}):null}}),a.accessor("component",{header:"位置",cell:r=>{const s=r.getValue();return s?e.jsx(n,{variant:"outline",className:"bg-green-50 text-green-700 border-green-200 font-mono text-xs",children:s}):null}}),a.display({id:"actions",header:({table:r})=>e.jsx("div",{className:" flex",children:e.jsxs(c,{children:[e.jsx(i,{asChild:!0,children:e.jsxs(l,{variant:"ghost",className:"ml-auto",children:["属性筛选",e.jsx(C,{})]})}),e.jsx(d,{align:"end",children:r.getAllColumns().filter(s=>s.getCanHide()).map(s=>e.jsx(p,{className:"capitalize",checked:s.getIsVisible(),onCheckedChange:h=>s.toggleVisibility(!!h),children:s.id},s.id))})]})}),cell:({row:r})=>e.jsx("div",{className:" flex justify-end",children:e.jsxs(c,{children:[e.jsx(i,{asChild:!0,children:e.jsx(l,{variant:"ghost",className:"h-8 p-0",children:e.jsx(y,{className:"h-4 w-4"})})}),e.jsxs(d,{align:"end",children:[e.jsx(t,{onClick:()=>{g(r.original.id)},children:"新增权限节点"}),e.jsx(t,{onClick:()=>{m(r.original)},children:"编辑"}),e.jsx(t,{className:"text-red-600",onClick:()=>u(r.original.id),children:"删除"})]})]})})})]}const A=Object.freeze(Object.defineProperty({__proto__:null,default:k},Symbol.toStringTag,{value:"Module"}));export{k as U,A as _};
