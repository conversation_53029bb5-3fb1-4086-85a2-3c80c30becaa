import{j as s,o as h}from"./index-Cq3zfZON.js";import{c as se,P as L}from"./button-CTQLK12e.js";import{P as O}from"./index-VR2XJ8FE.js";import{u as A,c as F}from"./index-CGKXfO_7.js";import{u as P}from"./index-BgXqfL-L.js";import{u as ce}from"./index-5UnhShgU.js";import{u as ae}from"./index-anJos_f1.js";import{c as C}from"./index-Dc_FVRD7.js";function ie(e,[o,r]){return Math.min(r,Math.max(o,e))}function de(e,o){return s.useReducer((r,l)=>o[r][l]??r,e)}var U="ScrollArea",[$,We]=se(U),[ue,v]=$(U),q=s.forwardRef((e,o)=>{const{__scopeScrollArea:r,type:l="hover",dir:t,scrollHideDelay:n=600,...c}=e,[a,i]=s.useState(null),[f,d]=s.useState(null),[b,u]=s.useState(null),[S,w]=s.useState(null),[T,X]=s.useState(null),[x,_]=s.useState(0),[Y,D]=s.useState(0),[j,y]=s.useState(!1),[N,W]=s.useState(!1),m=A(o,E=>i(E)),p=ce(t);return h.jsx(ue,{scope:r,type:l,dir:p,scrollHideDelay:n,scrollArea:a,viewport:f,onViewportChange:d,content:b,onContentChange:u,scrollbarX:S,onScrollbarXChange:w,scrollbarXEnabled:j,onScrollbarXEnabledChange:y,scrollbarY:T,onScrollbarYChange:X,scrollbarYEnabled:N,onScrollbarYEnabledChange:W,onCornerWidthChange:_,onCornerHeightChange:D,children:h.jsx(L.div,{dir:p,...c,ref:m,style:{position:"relative","--radix-scroll-area-corner-width":x+"px","--radix-scroll-area-corner-height":Y+"px",...e.style}})})});q.displayName=U;var G="ScrollAreaViewport",J=s.forwardRef((e,o)=>{const{__scopeScrollArea:r,children:l,nonce:t,...n}=e,c=v(G,r),a=s.useRef(null),i=A(o,a,c.onViewportChange);return h.jsxs(h.Fragment,{children:[h.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:t}),h.jsx(L.div,{"data-radix-scroll-area-viewport":"",...n,ref:i,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:h.jsx("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:l})})]})});J.displayName=G;var g="ScrollAreaScrollbar",K=s.forwardRef((e,o)=>{const{forceMount:r,...l}=e,t=v(g,e.__scopeScrollArea),{onScrollbarXEnabledChange:n,onScrollbarYEnabledChange:c}=t,a=e.orientation==="horizontal";return s.useEffect(()=>(a?n(!0):c(!0),()=>{a?n(!1):c(!1)}),[a,n,c]),t.type==="hover"?h.jsx(fe,{...l,ref:o,forceMount:r}):t.type==="scroll"?h.jsx(he,{...l,ref:o,forceMount:r}):t.type==="auto"?h.jsx(Q,{...l,ref:o,forceMount:r}):t.type==="always"?h.jsx(V,{...l,ref:o}):null});K.displayName=g;var fe=s.forwardRef((e,o)=>{const{forceMount:r,...l}=e,t=v(g,e.__scopeScrollArea),[n,c]=s.useState(!1);return s.useEffect(()=>{const a=t.scrollArea;let i=0;if(a){const f=()=>{window.clearTimeout(i),c(!0)},d=()=>{i=window.setTimeout(()=>c(!1),t.scrollHideDelay)};return a.addEventListener("pointerenter",f),a.addEventListener("pointerleave",d),()=>{window.clearTimeout(i),a.removeEventListener("pointerenter",f),a.removeEventListener("pointerleave",d)}}},[t.scrollArea,t.scrollHideDelay]),h.jsx(O,{present:r||n,children:h.jsx(Q,{"data-state":n?"visible":"hidden",...l,ref:o})})}),he=s.forwardRef((e,o)=>{const{forceMount:r,...l}=e,t=v(g,e.__scopeScrollArea),n=e.orientation==="horizontal",c=M(()=>i("SCROLL_END"),100),[a,i]=de("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return s.useEffect(()=>{if(a==="idle"){const f=window.setTimeout(()=>i("HIDE"),t.scrollHideDelay);return()=>window.clearTimeout(f)}},[a,t.scrollHideDelay,i]),s.useEffect(()=>{const f=t.viewport,d=n?"scrollLeft":"scrollTop";if(f){let b=f[d];const u=()=>{const S=f[d];b!==S&&(i("SCROLL"),c()),b=S};return f.addEventListener("scroll",u),()=>f.removeEventListener("scroll",u)}},[t.viewport,n,i,c]),h.jsx(O,{present:r||a!=="hidden",children:h.jsx(V,{"data-state":a==="hidden"?"hidden":"visible",...l,ref:o,onPointerEnter:C(e.onPointerEnter,()=>i("POINTER_ENTER")),onPointerLeave:C(e.onPointerLeave,()=>i("POINTER_LEAVE"))})})}),Q=s.forwardRef((e,o)=>{const r=v(g,e.__scopeScrollArea),{forceMount:l,...t}=e,[n,c]=s.useState(!1),a=e.orientation==="horizontal",i=M(()=>{if(r.viewport){const f=r.viewport.offsetWidth<r.viewport.scrollWidth,d=r.viewport.offsetHeight<r.viewport.scrollHeight;c(a?f:d)}},10);return R(r.viewport,i),R(r.content,i),h.jsx(O,{present:l||n,children:h.jsx(V,{"data-state":n?"visible":"hidden",...t,ref:o})})}),V=s.forwardRef((e,o)=>{const{orientation:r="vertical",...l}=e,t=v(g,e.__scopeScrollArea),n=s.useRef(null),c=s.useRef(0),[a,i]=s.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),f=te(a.viewport,a.content),d={...l,sizes:a,onSizesChange:i,hasThumb:f>0&&f<1,onThumbChange:u=>n.current=u,onThumbPointerUp:()=>c.current=0,onThumbPointerDown:u=>c.current=u};function b(u,S){return we(u,c.current,a,S)}return r==="horizontal"?h.jsx(be,{...d,ref:o,onThumbPositionChange:()=>{if(t.viewport&&n.current){const u=t.viewport.scrollLeft,S=k(u,a,t.dir);n.current.style.transform=`translate3d(${S}px, 0, 0)`}},onWheelScroll:u=>{t.viewport&&(t.viewport.scrollLeft=u)},onDragScroll:u=>{t.viewport&&(t.viewport.scrollLeft=b(u,t.dir))}}):r==="vertical"?h.jsx(Se,{...d,ref:o,onThumbPositionChange:()=>{if(t.viewport&&n.current){const u=t.viewport.scrollTop,S=k(u,a);n.current.style.transform=`translate3d(0, ${S}px, 0)`}},onWheelScroll:u=>{t.viewport&&(t.viewport.scrollTop=u)},onDragScroll:u=>{t.viewport&&(t.viewport.scrollTop=b(u))}}):null}),be=s.forwardRef((e,o)=>{const{sizes:r,onSizesChange:l,...t}=e,n=v(g,e.__scopeScrollArea),[c,a]=s.useState(),i=s.useRef(null),f=A(o,i,n.onScrollbarXChange);return s.useEffect(()=>{i.current&&a(getComputedStyle(i.current))},[i]),h.jsx(ee,{"data-orientation":"horizontal",...t,ref:f,sizes:r,style:{bottom:0,left:n.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:n.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":I(r)+"px",...e.style},onThumbPointerDown:d=>e.onThumbPointerDown(d.x),onDragScroll:d=>e.onDragScroll(d.x),onWheelScroll:(d,b)=>{if(n.viewport){const u=n.viewport.scrollLeft+d.deltaX;e.onWheelScroll(u),le(u,b)&&d.preventDefault()}},onResize:()=>{i.current&&n.viewport&&c&&l({content:n.viewport.scrollWidth,viewport:n.viewport.offsetWidth,scrollbar:{size:i.current.clientWidth,paddingStart:z(c.paddingLeft),paddingEnd:z(c.paddingRight)}})}})}),Se=s.forwardRef((e,o)=>{const{sizes:r,onSizesChange:l,...t}=e,n=v(g,e.__scopeScrollArea),[c,a]=s.useState(),i=s.useRef(null),f=A(o,i,n.onScrollbarYChange);return s.useEffect(()=>{i.current&&a(getComputedStyle(i.current))},[i]),h.jsx(ee,{"data-orientation":"vertical",...t,ref:f,sizes:r,style:{top:0,right:n.dir==="ltr"?0:void 0,left:n.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":I(r)+"px",...e.style},onThumbPointerDown:d=>e.onThumbPointerDown(d.y),onDragScroll:d=>e.onDragScroll(d.y),onWheelScroll:(d,b)=>{if(n.viewport){const u=n.viewport.scrollTop+d.deltaY;e.onWheelScroll(u),le(u,b)&&d.preventDefault()}},onResize:()=>{i.current&&n.viewport&&c&&l({content:n.viewport.scrollHeight,viewport:n.viewport.offsetHeight,scrollbar:{size:i.current.clientHeight,paddingStart:z(c.paddingTop),paddingEnd:z(c.paddingBottom)}})}})}),[me,Z]=$(g),ee=s.forwardRef((e,o)=>{const{__scopeScrollArea:r,sizes:l,hasThumb:t,onThumbChange:n,onThumbPointerUp:c,onThumbPointerDown:a,onThumbPositionChange:i,onDragScroll:f,onWheelScroll:d,onResize:b,...u}=e,S=v(g,r),[w,T]=s.useState(null),X=A(o,m=>T(m)),x=s.useRef(null),_=s.useRef(""),Y=S.viewport,D=l.content-l.viewport,j=P(d),y=P(i),N=M(b,10);function W(m){if(x.current){const p=m.clientX-x.current.left,E=m.clientY-x.current.top;f({x:p,y:E})}}return s.useEffect(()=>{const m=p=>{const E=p.target;w?.contains(E)&&j(p,D)};return document.addEventListener("wheel",m,{passive:!1}),()=>document.removeEventListener("wheel",m,{passive:!1})},[Y,w,D,j]),s.useEffect(y,[l,y]),R(w,N),R(S.content,N),h.jsx(me,{scope:r,scrollbar:w,hasThumb:t,onThumbChange:P(n),onThumbPointerUp:P(c),onThumbPositionChange:y,onThumbPointerDown:P(a),children:h.jsx(L.div,{...u,ref:X,style:{position:"absolute",...u.style},onPointerDown:C(e.onPointerDown,m=>{m.button===0&&(m.target.setPointerCapture(m.pointerId),x.current=w.getBoundingClientRect(),_.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",S.viewport&&(S.viewport.style.scrollBehavior="auto"),W(m))}),onPointerMove:C(e.onPointerMove,W),onPointerUp:C(e.onPointerUp,m=>{const p=m.target;p.hasPointerCapture(m.pointerId)&&p.releasePointerCapture(m.pointerId),document.body.style.webkitUserSelect=_.current,S.viewport&&(S.viewport.style.scrollBehavior=""),x.current=null})})})}),H="ScrollAreaThumb",re=s.forwardRef((e,o)=>{const{forceMount:r,...l}=e,t=Z(H,e.__scopeScrollArea);return h.jsx(O,{present:r||t.hasThumb,children:h.jsx(ve,{ref:o,...l})})}),ve=s.forwardRef((e,o)=>{const{__scopeScrollArea:r,style:l,...t}=e,n=v(H,r),c=Z(H,r),{onThumbPositionChange:a}=c,i=A(o,b=>c.onThumbChange(b)),f=s.useRef(void 0),d=M(()=>{f.current&&(f.current(),f.current=void 0)},100);return s.useEffect(()=>{const b=n.viewport;if(b){const u=()=>{if(d(),!f.current){const S=ge(b,a);f.current=S,a()}};return a(),b.addEventListener("scroll",u),()=>b.removeEventListener("scroll",u)}},[n.viewport,d,a]),h.jsx(L.div,{"data-state":c.hasThumb?"visible":"hidden",...t,ref:i,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...l},onPointerDownCapture:C(e.onPointerDownCapture,b=>{const S=b.target.getBoundingClientRect(),w=b.clientX-S.left,T=b.clientY-S.top;c.onThumbPointerDown({x:w,y:T})}),onPointerUp:C(e.onPointerUp,c.onThumbPointerUp)})});re.displayName=H;var B="ScrollAreaCorner",oe=s.forwardRef((e,o)=>{const r=v(B,e.__scopeScrollArea),l=!!(r.scrollbarX&&r.scrollbarY);return r.type!=="scroll"&&l?h.jsx(pe,{...e,ref:o}):null});oe.displayName=B;var pe=s.forwardRef((e,o)=>{const{__scopeScrollArea:r,...l}=e,t=v(B,r),[n,c]=s.useState(0),[a,i]=s.useState(0),f=!!(n&&a);return R(t.scrollbarX,()=>{const d=t.scrollbarX?.offsetHeight||0;t.onCornerHeightChange(d),i(d)}),R(t.scrollbarY,()=>{const d=t.scrollbarY?.offsetWidth||0;t.onCornerWidthChange(d),c(d)}),f?h.jsx(L.div,{...l,ref:o,style:{width:n,height:a,position:"absolute",right:t.dir==="ltr"?0:void 0,left:t.dir==="rtl"?0:void 0,bottom:0,...e.style}}):null});function z(e){return e?parseInt(e,10):0}function te(e,o){const r=e/o;return isNaN(r)?0:r}function I(e){const o=te(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,l=(e.scrollbar.size-r)*o;return Math.max(l,18)}function we(e,o,r,l="ltr"){const t=I(r),n=t/2,c=o||n,a=t-c,i=r.scrollbar.paddingStart+c,f=r.scrollbar.size-r.scrollbar.paddingEnd-a,d=r.content-r.viewport,b=l==="ltr"?[0,d]:[d*-1,0];return ne([i,f],b)(e)}function k(e,o,r="ltr"){const l=I(o),t=o.scrollbar.paddingStart+o.scrollbar.paddingEnd,n=o.scrollbar.size-t,c=o.content-o.viewport,a=n-l,i=r==="ltr"?[0,c]:[c*-1,0],f=ie(e,i);return ne([0,c],[0,a])(f)}function ne(e,o){return r=>{if(e[0]===e[1]||o[0]===o[1])return o[0];const l=(o[1]-o[0])/(e[1]-e[0]);return o[0]+l*(r-e[0])}}function le(e,o){return e>0&&e<o}var ge=(e,o=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},l=0;return function t(){const n={left:e.scrollLeft,top:e.scrollTop},c=r.left!==n.left,a=r.top!==n.top;(c||a)&&o(),r=n,l=window.requestAnimationFrame(t)}(),()=>window.cancelAnimationFrame(l)};function M(e,o){const r=P(e),l=s.useRef(0);return s.useEffect(()=>()=>window.clearTimeout(l.current),[]),s.useCallback(()=>{window.clearTimeout(l.current),l.current=window.setTimeout(r,o)},[r,o])}function R(e,o){const r=P(o);ae(()=>{let l=0;if(e){const t=new ResizeObserver(()=>{cancelAnimationFrame(l),l=window.requestAnimationFrame(r)});return t.observe(e),()=>{window.cancelAnimationFrame(l),t.unobserve(e)}}},[e,r])}var xe=q,Pe=J,Ce=oe;function He({className:e,children:o,...r}){return h.jsxs(xe,{"data-slot":"scroll-area",className:F("relative",e),...r,children:[h.jsx(Pe,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:o}),h.jsx(Ee,{}),h.jsx(Ce,{})]})}function Ee({className:e,orientation:o="vertical",...r}){return h.jsx(K,{"data-slot":"scroll-area-scrollbar",orientation:o,className:F("flex touch-none p-px transition-colors select-none",o==="vertical"&&"h-full w-2.5 border-l border-l-transparent",o==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent",e),...r,children:h.jsx(re,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}export{He as S};
