import{F as Vt,j as a,o as i}from"./index-Cq3zfZON.js";import{c as g}from"./index-Dc_FVRD7.js";import{u as k,d as qt,b as Fe,c as W}from"./index-CGKXfO_7.js";import{P as A,d as ke,c as le}from"./button-CTQLK12e.js";import{u as Le}from"./index-MsHvSdph.js";import{c as Ge}from"./index-BBWlQkPG.js";import{u as $e}from"./index-5UnhShgU.js";import{u as Z}from"./index-BgXqfL-L.js";import{u as Zt,P as Jt,h as Qt,R as en,F as tn}from"./index-erjOU2F6.js";import{u as nn,R as on,o as rn,s as an,f as sn,a as cn,b as un,h as dn,l as ln,c as pn}from"./index-fbjW5b5I.js";import{u as Ae}from"./index-anJos_f1.js";import{u as fn}from"./index-C2Z7v5r4.js";import{P as pe}from"./index-VR2XJ8FE.js";import{u as we}from"./index-CnargNwx.js";import{C as mn}from"./check-BfG1ovsW.js";/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vn=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],hn=Vt("circle",vn);var gn="DismissableLayer",xe="dismissableLayer.update",wn="dismissableLayer.pointerDownOutside",xn="dismissableLayer.focusOutside",Oe,Ke=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Be=a.forwardRef((e,n)=>{const{disableOutsidePointerEvents:t=!1,onEscapeKeyDown:o,onPointerDownOutside:r,onFocusOutside:s,onInteractOutside:u,onDismiss:p,...m}=e,l=a.useContext(Ke),[c,d]=a.useState(null),f=c?.ownerDocument??globalThis?.document,[,M]=a.useState({}),h=k(n,w=>d(w)),C=Array.from(l.layers),[x]=[...l.layersWithOutsidePointerEventsDisabled].slice(-1),b=C.indexOf(x),T=c?C.indexOf(c):-1,P=l.layersWithOutsidePointerEventsDisabled.size>0,_=T>=b,L=yn(w=>{const y=w.target,O=[...l.branches].some(I=>I.contains(y));!_||O||(r?.(w),u?.(w),w.defaultPrevented||p?.())},f),j=bn(w=>{const y=w.target;[...l.branches].some(I=>I.contains(y))||(s?.(w),u?.(w),w.defaultPrevented||p?.())},f);return Zt(w=>{T===l.layers.size-1&&(o?.(w),!w.defaultPrevented&&p&&(w.preventDefault(),p()))},f),a.useEffect(()=>{if(c)return t&&(l.layersWithOutsidePointerEventsDisabled.size===0&&(Oe=f.body.style.pointerEvents,f.body.style.pointerEvents="none"),l.layersWithOutsidePointerEventsDisabled.add(c)),l.layers.add(c),Ne(),()=>{t&&l.layersWithOutsidePointerEventsDisabled.size===1&&(f.body.style.pointerEvents=Oe)}},[c,f,t,l]),a.useEffect(()=>()=>{c&&(l.layers.delete(c),l.layersWithOutsidePointerEventsDisabled.delete(c),Ne())},[c,l]),a.useEffect(()=>{const w=()=>M({});return document.addEventListener(xe,w),()=>document.removeEventListener(xe,w)},[]),i.jsx(A.div,{...m,ref:h,style:{pointerEvents:P?_?"auto":"none":void 0,...e.style},onFocusCapture:g(e.onFocusCapture,j.onFocusCapture),onBlurCapture:g(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:g(e.onPointerDownCapture,L.onPointerDownCapture)})});Be.displayName=gn;var Mn="DismissableLayerBranch",Cn=a.forwardRef((e,n)=>{const t=a.useContext(Ke),o=a.useRef(null),r=k(n,o);return a.useEffect(()=>{const s=o.current;if(s)return t.branches.add(s),()=>{t.branches.delete(s)}},[t.branches]),i.jsx(A.div,{...e,ref:r})});Cn.displayName=Mn;function yn(e,n=globalThis?.document){const t=Z(e),o=a.useRef(!1),r=a.useRef(()=>{});return a.useEffect(()=>{const s=p=>{if(p.target&&!o.current){let m=function(){Ue(wn,t,l,{discrete:!0})};const l={originalEvent:p};p.pointerType==="touch"?(n.removeEventListener("click",r.current),r.current=m,n.addEventListener("click",r.current,{once:!0})):m()}else n.removeEventListener("click",r.current);o.current=!1},u=window.setTimeout(()=>{n.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(u),n.removeEventListener("pointerdown",s),n.removeEventListener("click",r.current)}},[n,t]),{onPointerDownCapture:()=>o.current=!0}}function bn(e,n=globalThis?.document){const t=Z(e),o=a.useRef(!1);return a.useEffect(()=>{const r=s=>{s.target&&!o.current&&Ue(xn,t,{originalEvent:s},{discrete:!1})};return n.addEventListener("focusin",r),()=>n.removeEventListener("focusin",r)},[n,t]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}function Ne(){const e=new CustomEvent(xe);document.dispatchEvent(e)}function Ue(e,n,t,{discrete:o}){const r=t.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:t});n&&r.addEventListener(e,n,{once:!0}),o?ke(r,s):r.dispatchEvent(s)}var ye="Popper",[ze,We]=le(ye),[En,He]=ze(ye),Ye=e=>{const{__scopePopper:n,children:t}=e,[o,r]=a.useState(null);return i.jsx(En,{scope:n,anchor:o,onAnchorChange:r,children:t})};Ye.displayName=ye;var Xe="PopperAnchor",Ve=a.forwardRef((e,n)=>{const{__scopePopper:t,virtualRef:o,...r}=e,s=He(Xe,t),u=a.useRef(null),p=k(n,u);return a.useEffect(()=>{s.onAnchorChange(o?.current||u.current)}),o?null:i.jsx(A.div,{...r,ref:p})});Ve.displayName=Xe;var be="PopperContent",[_n,Pn]=ze(be),qe=a.forwardRef((e,n)=>{const{__scopePopper:t,side:o="bottom",sideOffset:r=0,align:s="center",alignOffset:u=0,arrowPadding:p=0,avoidCollisions:m=!0,collisionBoundary:l=[],collisionPadding:c=0,sticky:d="partial",hideWhenDetached:f=!1,updatePositionStrategy:M="optimized",onPlaced:h,...C}=e,x=He(be,t),[b,T]=a.useState(null),P=k(n,E=>T(E)),[_,L]=a.useState(null),j=fn(_),w=j?.width??0,y=j?.height??0,O=o+(s!=="center"?"-"+s:""),I=typeof c=="number"?c:{top:0,right:0,bottom:0,left:0,...c},$=Array.isArray(l)?l:[l],B=$.length>0,G={padding:I,boundary:$.filter(In),altBoundary:B},{refs:X,floatingStyles:V,placement:N,isPositioned:q,middlewareData:D}=nn({strategy:"fixed",placement:O,whileElementsMounted:(...E)=>pn(...E,{animationFrame:M==="always"}),elements:{reference:x.anchor},middleware:[rn({mainAxis:r+y,alignmentAxis:u}),m&&an({mainAxis:!0,crossAxis:!1,limiter:d==="partial"?ln():void 0,...G}),m&&sn({...G}),cn({...G,apply:({elements:E,rects:ee,availableWidth:Wt,availableHeight:Ht})=>{const{width:Yt,height:Xt}=ee.reference,ce=E.floating.style;ce.setProperty("--radix-popper-available-width",`${Wt}px`),ce.setProperty("--radix-popper-available-height",`${Ht}px`),ce.setProperty("--radix-popper-anchor-width",`${Yt}px`),ce.setProperty("--radix-popper-anchor-height",`${Xt}px`)}}),_&&un({element:_,padding:p}),Dn({arrowWidth:w,arrowHeight:y}),f&&dn({strategy:"referenceHidden",...G})]}),[v,K]=Qe(N),F=Z(h);Ae(()=>{q&&F?.()},[q,F]);const U=D.arrow?.x,J=D.arrow?.y,Q=D.arrow?.centerOffset!==0,[ie,z]=a.useState();return Ae(()=>{b&&z(window.getComputedStyle(b).zIndex)},[b]),i.jsx("div",{ref:X.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:q?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ie,"--radix-popper-transform-origin":[D.transformOrigin?.x,D.transformOrigin?.y].join(" "),...D.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:i.jsx(_n,{scope:t,placedSide:v,onArrowChange:L,arrowX:U,arrowY:J,shouldHideArrow:Q,children:i.jsx(A.div,{"data-side":v,"data-align":K,...C,ref:P,style:{...C.style,animation:q?void 0:"none"}})})})});qe.displayName=be;var Ze="PopperArrow",Rn={top:"bottom",right:"left",bottom:"top",left:"right"},Je=a.forwardRef(function(n,t){const{__scopePopper:o,...r}=n,s=Pn(Ze,o),u=Rn[s.placedSide];return i.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[u]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:i.jsx(on,{...r,ref:t,style:{...r.style,display:"block"}})})});Je.displayName=Ze;function In(e){return e!==null}var Dn=e=>({name:"transformOrigin",options:e,fn(n){const{placement:t,rects:o,middlewareData:r}=n,u=r.arrow?.centerOffset!==0,p=u?0:e.arrowWidth,m=u?0:e.arrowHeight,[l,c]=Qe(t),d={start:"0%",center:"50%",end:"100%"}[c],f=(r.arrow?.x??0)+p/2,M=(r.arrow?.y??0)+m/2;let h="",C="";return l==="bottom"?(h=u?d:`${f}px`,C=`${-m}px`):l==="top"?(h=u?d:`${f}px`,C=`${o.floating.height+m}px`):l==="right"?(h=`${-m}px`,C=u?d:`${M}px`):l==="left"&&(h=`${o.floating.width+m}px`,C=u?d:`${M}px`),{data:{x:h,y:C}}}});function Qe(e){const[n,t="center"]=e.split("-");return[n,t]}var Sn=Ye,An=Ve,On=qe,Nn=Je,he=0;function Tn(){a.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Te()),document.body.insertAdjacentElement("beforeend",e[1]??Te()),he++,()=>{he===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(n=>n.remove()),he--}},[])}function Te(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var ge="rovingFocusGroup.onEntryFocus",jn={bubbles:!1,cancelable:!0},re="RovingFocusGroup",[Me,et,Fn]=Ge(re),[kn,tt]=le(re,[Fn]),[Ln,Gn]=kn(re),nt=a.forwardRef((e,n)=>i.jsx(Me.Provider,{scope:e.__scopeRovingFocusGroup,children:i.jsx(Me.Slot,{scope:e.__scopeRovingFocusGroup,children:i.jsx($n,{...e,ref:n})})}));nt.displayName=re;var $n=a.forwardRef((e,n)=>{const{__scopeRovingFocusGroup:t,orientation:o,loop:r=!1,dir:s,currentTabStopId:u,defaultCurrentTabStopId:p,onCurrentTabStopIdChange:m,onEntryFocus:l,preventScrollOnEntryFocus:c=!1,...d}=e,f=a.useRef(null),M=k(n,f),h=$e(s),[C,x]=Le({prop:u,defaultProp:p??null,onChange:m,caller:re}),[b,T]=a.useState(!1),P=Z(l),_=et(t),L=a.useRef(!1),[j,w]=a.useState(0);return a.useEffect(()=>{const y=f.current;if(y)return y.addEventListener(ge,P),()=>y.removeEventListener(ge,P)},[P]),i.jsx(Ln,{scope:t,orientation:o,dir:h,loop:r,currentTabStopId:C,onItemFocus:a.useCallback(y=>x(y),[x]),onItemShiftTab:a.useCallback(()=>T(!0),[]),onFocusableItemAdd:a.useCallback(()=>w(y=>y+1),[]),onFocusableItemRemove:a.useCallback(()=>w(y=>y-1),[]),children:i.jsx(A.div,{tabIndex:b||j===0?-1:0,"data-orientation":o,...d,ref:M,style:{outline:"none",...e.style},onMouseDown:g(e.onMouseDown,()=>{L.current=!0}),onFocus:g(e.onFocus,y=>{const O=!L.current;if(y.target===y.currentTarget&&O&&!b){const I=new CustomEvent(ge,jn);if(y.currentTarget.dispatchEvent(I),!I.defaultPrevented){const $=_().filter(N=>N.focusable),B=$.find(N=>N.active),G=$.find(N=>N.id===C),V=[B,G,...$].filter(Boolean).map(N=>N.ref.current);at(V,c)}}L.current=!1}),onBlur:g(e.onBlur,()=>T(!1))})})}),ot="RovingFocusGroupItem",rt=a.forwardRef((e,n)=>{const{__scopeRovingFocusGroup:t,focusable:o=!0,active:r=!1,tabStopId:s,children:u,...p}=e,m=we(),l=s||m,c=Gn(ot,t),d=c.currentTabStopId===l,f=et(t),{onFocusableItemAdd:M,onFocusableItemRemove:h,currentTabStopId:C}=c;return a.useEffect(()=>{if(o)return M(),()=>h()},[o,M,h]),i.jsx(Me.ItemSlot,{scope:t,id:l,focusable:o,active:r,children:i.jsx(A.span,{tabIndex:d?0:-1,"data-orientation":c.orientation,...p,ref:n,onMouseDown:g(e.onMouseDown,x=>{o?c.onItemFocus(l):x.preventDefault()}),onFocus:g(e.onFocus,()=>c.onItemFocus(l)),onKeyDown:g(e.onKeyDown,x=>{if(x.key==="Tab"&&x.shiftKey){c.onItemShiftTab();return}if(x.target!==x.currentTarget)return;const b=Un(x,c.orientation,c.dir);if(b!==void 0){if(x.metaKey||x.ctrlKey||x.altKey||x.shiftKey)return;x.preventDefault();let P=f().filter(_=>_.focusable).map(_=>_.ref.current);if(b==="last")P.reverse();else if(b==="prev"||b==="next"){b==="prev"&&P.reverse();const _=P.indexOf(x.currentTarget);P=c.loop?zn(P,_+1):P.slice(_+1)}setTimeout(()=>at(P))}}),children:typeof u=="function"?u({isCurrentTabStop:d,hasTabStop:C!=null}):u})})});rt.displayName=ot;var Kn={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Bn(e,n){return n!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function Un(e,n,t){const o=Bn(e.key,t);if(!(n==="vertical"&&["ArrowLeft","ArrowRight"].includes(o))&&!(n==="horizontal"&&["ArrowUp","ArrowDown"].includes(o)))return Kn[o]}function at(e,n=!1){const t=document.activeElement;for(const o of e)if(o===t||(o.focus({preventScroll:n}),document.activeElement!==t))return}function zn(e,n){return e.map((t,o)=>e[(n+o)%e.length])}var Wn=nt,Hn=rt,Ce=["Enter"," "],Yn=["ArrowDown","PageUp","Home"],st=["ArrowUp","PageDown","End"],Xn=[...Yn,...st],Vn={ltr:[...Ce,"ArrowRight"],rtl:[...Ce,"ArrowLeft"]},qn={ltr:["ArrowLeft"],rtl:["ArrowRight"]},ae="Menu",[ne,Zn,Jn]=Ge(ae),[H,it]=le(ae,[Jn,We,tt]),fe=We(),ct=tt(),[Qn,Y]=H(ae),[eo,se]=H(ae),ut=e=>{const{__scopeMenu:n,open:t=!1,children:o,dir:r,onOpenChange:s,modal:u=!0}=e,p=fe(n),[m,l]=a.useState(null),c=a.useRef(!1),d=Z(s),f=$e(r);return a.useEffect(()=>{const M=()=>{c.current=!0,document.addEventListener("pointerdown",h,{capture:!0,once:!0}),document.addEventListener("pointermove",h,{capture:!0,once:!0})},h=()=>c.current=!1;return document.addEventListener("keydown",M,{capture:!0}),()=>{document.removeEventListener("keydown",M,{capture:!0}),document.removeEventListener("pointerdown",h,{capture:!0}),document.removeEventListener("pointermove",h,{capture:!0})}},[]),i.jsx(Sn,{...p,children:i.jsx(Qn,{scope:n,open:t,onOpenChange:d,content:m,onContentChange:l,children:i.jsx(eo,{scope:n,onClose:a.useCallback(()=>d(!1),[d]),isUsingKeyboardRef:c,dir:f,modal:u,children:o})})})};ut.displayName=ae;var to="MenuAnchor",Ee=a.forwardRef((e,n)=>{const{__scopeMenu:t,...o}=e,r=fe(t);return i.jsx(An,{...r,...o,ref:n})});Ee.displayName=to;var _e="MenuPortal",[no,dt]=H(_e,{forceMount:void 0}),lt=e=>{const{__scopeMenu:n,forceMount:t,children:o,container:r}=e,s=Y(_e,n);return i.jsx(no,{scope:n,forceMount:t,children:i.jsx(pe,{present:t||s.open,children:i.jsx(Jt,{asChild:!0,container:r,children:o})})})};lt.displayName=_e;var S="MenuContent",[oo,Pe]=H(S),pt=a.forwardRef((e,n)=>{const t=dt(S,e.__scopeMenu),{forceMount:o=t.forceMount,...r}=e,s=Y(S,e.__scopeMenu),u=se(S,e.__scopeMenu);return i.jsx(ne.Provider,{scope:e.__scopeMenu,children:i.jsx(pe,{present:o||s.open,children:i.jsx(ne.Slot,{scope:e.__scopeMenu,children:u.modal?i.jsx(ro,{...r,ref:n}):i.jsx(ao,{...r,ref:n})})})})}),ro=a.forwardRef((e,n)=>{const t=Y(S,e.__scopeMenu),o=a.useRef(null),r=k(n,o);return a.useEffect(()=>{const s=o.current;if(s)return Qt(s)},[]),i.jsx(Re,{...e,ref:r,trapFocus:t.open,disableOutsidePointerEvents:t.open,disableOutsideScroll:!0,onFocusOutside:g(e.onFocusOutside,s=>s.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>t.onOpenChange(!1)})}),ao=a.forwardRef((e,n)=>{const t=Y(S,e.__scopeMenu);return i.jsx(Re,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>t.onOpenChange(!1)})}),so=qt("MenuContent.ScrollLock"),Re=a.forwardRef((e,n)=>{const{__scopeMenu:t,loop:o=!1,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:u,disableOutsidePointerEvents:p,onEntryFocus:m,onEscapeKeyDown:l,onPointerDownOutside:c,onFocusOutside:d,onInteractOutside:f,onDismiss:M,disableOutsideScroll:h,...C}=e,x=Y(S,t),b=se(S,t),T=fe(t),P=ct(t),_=Zn(t),[L,j]=a.useState(null),w=a.useRef(null),y=k(n,w,x.onContentChange),O=a.useRef(0),I=a.useRef(""),$=a.useRef(0),B=a.useRef(null),G=a.useRef("right"),X=a.useRef(0),V=h?en:a.Fragment,N=h?{as:so,allowPinchZoom:!0}:void 0,q=v=>{const K=I.current+v,F=_().filter(E=>!E.disabled),U=document.activeElement,J=F.find(E=>E.ref.current===U)?.textValue,Q=F.map(E=>E.textValue),ie=xo(Q,K,J),z=F.find(E=>E.textValue===ie)?.ref.current;(function E(ee){I.current=ee,window.clearTimeout(O.current),ee!==""&&(O.current=window.setTimeout(()=>E(""),1e3))})(K),z&&setTimeout(()=>z.focus())};a.useEffect(()=>()=>window.clearTimeout(O.current),[]),Tn();const D=a.useCallback(v=>G.current===B.current?.side&&Co(v,B.current?.area),[]);return i.jsx(oo,{scope:t,searchRef:I,onItemEnter:a.useCallback(v=>{D(v)&&v.preventDefault()},[D]),onItemLeave:a.useCallback(v=>{D(v)||(w.current?.focus(),j(null))},[D]),onTriggerLeave:a.useCallback(v=>{D(v)&&v.preventDefault()},[D]),pointerGraceTimerRef:$,onPointerGraceIntentChange:a.useCallback(v=>{B.current=v},[]),children:i.jsx(V,{...N,children:i.jsx(tn,{asChild:!0,trapped:r,onMountAutoFocus:g(s,v=>{v.preventDefault(),w.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:i.jsx(Be,{asChild:!0,disableOutsidePointerEvents:p,onEscapeKeyDown:l,onPointerDownOutside:c,onFocusOutside:d,onInteractOutside:f,onDismiss:M,children:i.jsx(Wn,{asChild:!0,...P,dir:b.dir,orientation:"vertical",loop:o,currentTabStopId:L,onCurrentTabStopIdChange:j,onEntryFocus:g(m,v=>{b.isUsingKeyboardRef.current||v.preventDefault()}),preventScrollOnEntryFocus:!0,children:i.jsx(On,{role:"menu","aria-orientation":"vertical","data-state":It(x.open),"data-radix-menu-content":"",dir:b.dir,...T,...C,ref:y,style:{outline:"none",...C.style},onKeyDown:g(C.onKeyDown,v=>{const F=v.target.closest("[data-radix-menu-content]")===v.currentTarget,U=v.ctrlKey||v.altKey||v.metaKey,J=v.key.length===1;F&&(v.key==="Tab"&&v.preventDefault(),!U&&J&&q(v.key));const Q=w.current;if(v.target!==Q||!Xn.includes(v.key))return;v.preventDefault();const z=_().filter(E=>!E.disabled).map(E=>E.ref.current);st.includes(v.key)&&z.reverse(),go(z)}),onBlur:g(e.onBlur,v=>{v.currentTarget.contains(v.target)||(window.clearTimeout(O.current),I.current="")}),onPointerMove:g(e.onPointerMove,oe(v=>{const K=v.target,F=X.current!==v.clientX;if(v.currentTarget.contains(K)&&F){const U=v.clientX>X.current?"right":"left";G.current=U,X.current=v.clientX}}))})})})})})})});pt.displayName=S;var io="MenuGroup",Ie=a.forwardRef((e,n)=>{const{__scopeMenu:t,...o}=e;return i.jsx(A.div,{role:"group",...o,ref:n})});Ie.displayName=io;var co="MenuLabel",ft=a.forwardRef((e,n)=>{const{__scopeMenu:t,...o}=e;return i.jsx(A.div,{...o,ref:n})});ft.displayName=co;var ue="MenuItem",je="menu.itemSelect",me=a.forwardRef((e,n)=>{const{disabled:t=!1,onSelect:o,...r}=e,s=a.useRef(null),u=se(ue,e.__scopeMenu),p=Pe(ue,e.__scopeMenu),m=k(n,s),l=a.useRef(!1),c=()=>{const d=s.current;if(!t&&d){const f=new CustomEvent(je,{bubbles:!0,cancelable:!0});d.addEventListener(je,M=>o?.(M),{once:!0}),ke(d,f),f.defaultPrevented?l.current=!1:u.onClose()}};return i.jsx(mt,{...r,ref:m,disabled:t,onClick:g(e.onClick,c),onPointerDown:d=>{e.onPointerDown?.(d),l.current=!0},onPointerUp:g(e.onPointerUp,d=>{l.current||d.currentTarget?.click()}),onKeyDown:g(e.onKeyDown,d=>{const f=p.searchRef.current!=="";t||f&&d.key===" "||Ce.includes(d.key)&&(d.currentTarget.click(),d.preventDefault())})})});me.displayName=ue;var mt=a.forwardRef((e,n)=>{const{__scopeMenu:t,disabled:o=!1,textValue:r,...s}=e,u=Pe(ue,t),p=ct(t),m=a.useRef(null),l=k(n,m),[c,d]=a.useState(!1),[f,M]=a.useState("");return a.useEffect(()=>{const h=m.current;h&&M((h.textContent??"").trim())},[s.children]),i.jsx(ne.ItemSlot,{scope:t,disabled:o,textValue:r??f,children:i.jsx(Hn,{asChild:!0,...p,focusable:!o,children:i.jsx(A.div,{role:"menuitem","data-highlighted":c?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...s,ref:l,onPointerMove:g(e.onPointerMove,oe(h=>{o?u.onItemLeave(h):(u.onItemEnter(h),h.defaultPrevented||h.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:g(e.onPointerLeave,oe(h=>u.onItemLeave(h))),onFocus:g(e.onFocus,()=>d(!0)),onBlur:g(e.onBlur,()=>d(!1))})})})}),uo="MenuCheckboxItem",vt=a.forwardRef((e,n)=>{const{checked:t=!1,onCheckedChange:o,...r}=e;return i.jsx(Mt,{scope:e.__scopeMenu,checked:t,children:i.jsx(me,{role:"menuitemcheckbox","aria-checked":de(t)?"mixed":t,...r,ref:n,"data-state":Se(t),onSelect:g(r.onSelect,()=>o?.(de(t)?!0:!t),{checkForDefaultPrevented:!1})})})});vt.displayName=uo;var ht="MenuRadioGroup",[lo,po]=H(ht,{value:void 0,onValueChange:()=>{}}),gt=a.forwardRef((e,n)=>{const{value:t,onValueChange:o,...r}=e,s=Z(o);return i.jsx(lo,{scope:e.__scopeMenu,value:t,onValueChange:s,children:i.jsx(Ie,{...r,ref:n})})});gt.displayName=ht;var wt="MenuRadioItem",xt=a.forwardRef((e,n)=>{const{value:t,...o}=e,r=po(wt,e.__scopeMenu),s=t===r.value;return i.jsx(Mt,{scope:e.__scopeMenu,checked:s,children:i.jsx(me,{role:"menuitemradio","aria-checked":s,...o,ref:n,"data-state":Se(s),onSelect:g(o.onSelect,()=>r.onValueChange?.(t),{checkForDefaultPrevented:!1})})})});xt.displayName=wt;var De="MenuItemIndicator",[Mt,fo]=H(De,{checked:!1}),Ct=a.forwardRef((e,n)=>{const{__scopeMenu:t,forceMount:o,...r}=e,s=fo(De,t);return i.jsx(pe,{present:o||de(s.checked)||s.checked===!0,children:i.jsx(A.span,{...r,ref:n,"data-state":Se(s.checked)})})});Ct.displayName=De;var mo="MenuSeparator",yt=a.forwardRef((e,n)=>{const{__scopeMenu:t,...o}=e;return i.jsx(A.div,{role:"separator","aria-orientation":"horizontal",...o,ref:n})});yt.displayName=mo;var vo="MenuArrow",bt=a.forwardRef((e,n)=>{const{__scopeMenu:t,...o}=e,r=fe(t);return i.jsx(Nn,{...r,...o,ref:n})});bt.displayName=vo;var ho="MenuSub",[Pr,Et]=H(ho),te="MenuSubTrigger",_t=a.forwardRef((e,n)=>{const t=Y(te,e.__scopeMenu),o=se(te,e.__scopeMenu),r=Et(te,e.__scopeMenu),s=Pe(te,e.__scopeMenu),u=a.useRef(null),{pointerGraceTimerRef:p,onPointerGraceIntentChange:m}=s,l={__scopeMenu:e.__scopeMenu},c=a.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return a.useEffect(()=>c,[c]),a.useEffect(()=>{const d=p.current;return()=>{window.clearTimeout(d),m(null)}},[p,m]),i.jsx(Ee,{asChild:!0,...l,children:i.jsx(mt,{id:r.triggerId,"aria-haspopup":"menu","aria-expanded":t.open,"aria-controls":r.contentId,"data-state":It(t.open),...e,ref:Fe(n,r.onTriggerChange),onClick:d=>{e.onClick?.(d),!(e.disabled||d.defaultPrevented)&&(d.currentTarget.focus(),t.open||t.onOpenChange(!0))},onPointerMove:g(e.onPointerMove,oe(d=>{s.onItemEnter(d),!d.defaultPrevented&&!e.disabled&&!t.open&&!u.current&&(s.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{t.onOpenChange(!0),c()},100))})),onPointerLeave:g(e.onPointerLeave,oe(d=>{c();const f=t.content?.getBoundingClientRect();if(f){const M=t.content?.dataset.side,h=M==="right",C=h?-5:5,x=f[h?"left":"right"],b=f[h?"right":"left"];s.onPointerGraceIntentChange({area:[{x:d.clientX+C,y:d.clientY},{x,y:f.top},{x:b,y:f.top},{x:b,y:f.bottom},{x,y:f.bottom}],side:M}),window.clearTimeout(p.current),p.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(d),d.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:g(e.onKeyDown,d=>{const f=s.searchRef.current!=="";e.disabled||f&&d.key===" "||Vn[o.dir].includes(d.key)&&(t.onOpenChange(!0),t.content?.focus(),d.preventDefault())})})})});_t.displayName=te;var Pt="MenuSubContent",Rt=a.forwardRef((e,n)=>{const t=dt(S,e.__scopeMenu),{forceMount:o=t.forceMount,...r}=e,s=Y(S,e.__scopeMenu),u=se(S,e.__scopeMenu),p=Et(Pt,e.__scopeMenu),m=a.useRef(null),l=k(n,m);return i.jsx(ne.Provider,{scope:e.__scopeMenu,children:i.jsx(pe,{present:o||s.open,children:i.jsx(ne.Slot,{scope:e.__scopeMenu,children:i.jsx(Re,{id:p.contentId,"aria-labelledby":p.triggerId,...r,ref:l,align:"start",side:u.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:c=>{u.isUsingKeyboardRef.current&&m.current?.focus(),c.preventDefault()},onCloseAutoFocus:c=>c.preventDefault(),onFocusOutside:g(e.onFocusOutside,c=>{c.target!==p.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:g(e.onEscapeKeyDown,c=>{u.onClose(),c.preventDefault()}),onKeyDown:g(e.onKeyDown,c=>{const d=c.currentTarget.contains(c.target),f=qn[u.dir].includes(c.key);d&&f&&(s.onOpenChange(!1),p.trigger?.focus(),c.preventDefault())})})})})})});Rt.displayName=Pt;function It(e){return e?"open":"closed"}function de(e){return e==="indeterminate"}function Se(e){return de(e)?"indeterminate":e?"checked":"unchecked"}function go(e){const n=document.activeElement;for(const t of e)if(t===n||(t.focus(),document.activeElement!==n))return}function wo(e,n){return e.map((t,o)=>e[(n+o)%e.length])}function xo(e,n,t){const r=n.length>1&&Array.from(n).every(l=>l===n[0])?n[0]:n,s=t?e.indexOf(t):-1;let u=wo(e,Math.max(s,0));r.length===1&&(u=u.filter(l=>l!==t));const m=u.find(l=>l.toLowerCase().startsWith(r.toLowerCase()));return m!==t?m:void 0}function Mo(e,n){const{x:t,y:o}=e;let r=!1;for(let s=0,u=n.length-1;s<n.length;u=s++){const p=n[s],m=n[u],l=p.x,c=p.y,d=m.x,f=m.y;c>o!=f>o&&t<(d-l)*(o-c)/(f-c)+l&&(r=!r)}return r}function Co(e,n){if(!n)return!1;const t={x:e.clientX,y:e.clientY};return Mo(t,n)}function oe(e){return n=>n.pointerType==="mouse"?e(n):void 0}var yo=ut,bo=Ee,Eo=lt,_o=pt,Po=Ie,Ro=ft,Io=me,Do=vt,So=gt,Ao=xt,Oo=Ct,No=yt,To=bt,jo=_t,Fo=Rt,ve="DropdownMenu",[ko,Rr]=le(ve,[it]),R=it(),[Lo,Dt]=ko(ve),St=e=>{const{__scopeDropdownMenu:n,children:t,dir:o,open:r,defaultOpen:s,onOpenChange:u,modal:p=!0}=e,m=R(n),l=a.useRef(null),[c,d]=Le({prop:r,defaultProp:s??!1,onChange:u,caller:ve});return i.jsx(Lo,{scope:n,triggerId:we(),triggerRef:l,contentId:we(),open:c,onOpenChange:d,onOpenToggle:a.useCallback(()=>d(f=>!f),[d]),modal:p,children:i.jsx(yo,{...m,open:c,onOpenChange:d,dir:o,modal:p,children:t})})};St.displayName=ve;var At="DropdownMenuTrigger",Ot=a.forwardRef((e,n)=>{const{__scopeDropdownMenu:t,disabled:o=!1,...r}=e,s=Dt(At,t),u=R(t);return i.jsx(bo,{asChild:!0,...u,children:i.jsx(A.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":o?"":void 0,disabled:o,...r,ref:Fe(n,s.triggerRef),onPointerDown:g(e.onPointerDown,p=>{!o&&p.button===0&&p.ctrlKey===!1&&(s.onOpenToggle(),s.open||p.preventDefault())}),onKeyDown:g(e.onKeyDown,p=>{o||(["Enter"," "].includes(p.key)&&s.onOpenToggle(),p.key==="ArrowDown"&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(p.key)&&p.preventDefault())})})})});Ot.displayName=At;var Go="DropdownMenuPortal",Nt=e=>{const{__scopeDropdownMenu:n,...t}=e,o=R(n);return i.jsx(Eo,{...o,...t})};Nt.displayName=Go;var Tt="DropdownMenuContent",jt=a.forwardRef((e,n)=>{const{__scopeDropdownMenu:t,...o}=e,r=Dt(Tt,t),s=R(t),u=a.useRef(!1);return i.jsx(_o,{id:r.contentId,"aria-labelledby":r.triggerId,...s,...o,ref:n,onCloseAutoFocus:g(e.onCloseAutoFocus,p=>{u.current||r.triggerRef.current?.focus(),u.current=!1,p.preventDefault()}),onInteractOutside:g(e.onInteractOutside,p=>{const m=p.detail.originalEvent,l=m.button===0&&m.ctrlKey===!0,c=m.button===2||l;(!r.modal||c)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});jt.displayName=Tt;var $o="DropdownMenuGroup",Ft=a.forwardRef((e,n)=>{const{__scopeDropdownMenu:t,...o}=e,r=R(t);return i.jsx(Po,{...r,...o,ref:n})});Ft.displayName=$o;var Ko="DropdownMenuLabel",kt=a.forwardRef((e,n)=>{const{__scopeDropdownMenu:t,...o}=e,r=R(t);return i.jsx(Ro,{...r,...o,ref:n})});kt.displayName=Ko;var Bo="DropdownMenuItem",Lt=a.forwardRef((e,n)=>{const{__scopeDropdownMenu:t,...o}=e,r=R(t);return i.jsx(Io,{...r,...o,ref:n})});Lt.displayName=Bo;var Uo="DropdownMenuCheckboxItem",Gt=a.forwardRef((e,n)=>{const{__scopeDropdownMenu:t,...o}=e,r=R(t);return i.jsx(Do,{...r,...o,ref:n})});Gt.displayName=Uo;var zo="DropdownMenuRadioGroup",$t=a.forwardRef((e,n)=>{const{__scopeDropdownMenu:t,...o}=e,r=R(t);return i.jsx(So,{...r,...o,ref:n})});$t.displayName=zo;var Wo="DropdownMenuRadioItem",Kt=a.forwardRef((e,n)=>{const{__scopeDropdownMenu:t,...o}=e,r=R(t);return i.jsx(Ao,{...r,...o,ref:n})});Kt.displayName=Wo;var Ho="DropdownMenuItemIndicator",Bt=a.forwardRef((e,n)=>{const{__scopeDropdownMenu:t,...o}=e,r=R(t);return i.jsx(Oo,{...r,...o,ref:n})});Bt.displayName=Ho;var Yo="DropdownMenuSeparator",Ut=a.forwardRef((e,n)=>{const{__scopeDropdownMenu:t,...o}=e,r=R(t);return i.jsx(No,{...r,...o,ref:n})});Ut.displayName=Yo;var Xo="DropdownMenuArrow",Vo=a.forwardRef((e,n)=>{const{__scopeDropdownMenu:t,...o}=e,r=R(t);return i.jsx(To,{...r,...o,ref:n})});Vo.displayName=Xo;var qo="DropdownMenuSubTrigger",Zo=a.forwardRef((e,n)=>{const{__scopeDropdownMenu:t,...o}=e,r=R(t);return i.jsx(jo,{...r,...o,ref:n})});Zo.displayName=qo;var Jo="DropdownMenuSubContent",Qo=a.forwardRef((e,n)=>{const{__scopeDropdownMenu:t,...o}=e,r=R(t);return i.jsx(Fo,{...r,...o,ref:n,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Qo.displayName=Jo;var er=St,tr=Ot,nr=Nt,or=jt,rr=Ft,ar=kt,sr=Lt,ir=Gt,cr=$t,ur=Kt,zt=Bt,dr=Ut;function Ir({...e}){return i.jsx(er,{"data-slot":"dropdown-menu",...e})}function Dr({...e}){return i.jsx(tr,{"data-slot":"dropdown-menu-trigger",...e})}function Sr({className:e,sideOffset:n=4,...t}){return i.jsx(nr,{children:i.jsx(or,{"data-slot":"dropdown-menu-content",sideOffset:n,className:W("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...t})})}function Ar({...e}){return i.jsx(rr,{"data-slot":"dropdown-menu-group",...e})}function Or({className:e,inset:n,variant:t="default",...o}){return i.jsx(sr,{"data-slot":"dropdown-menu-item","data-inset":n,"data-variant":t,className:W("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...o})}function Nr({className:e,children:n,checked:t,...o}){return i.jsxs(ir,{"data-slot":"dropdown-menu-checkbox-item",className:W("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),checked:t,...o,children:[i.jsx("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:i.jsx(zt,{children:i.jsx(mn,{className:"size-4"})})}),n]})}function Tr({...e}){return i.jsx(cr,{"data-slot":"dropdown-menu-radio-group",...e})}function jr({className:e,children:n,...t}){return i.jsxs(ur,{"data-slot":"dropdown-menu-radio-item",className:W("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t,children:[i.jsx("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:i.jsx(zt,{children:i.jsx(hn,{className:"size-2 fill-current"})})}),n]})}function Fr({className:e,inset:n,...t}){return i.jsx(ar,{"data-slot":"dropdown-menu-label","data-inset":n,className:W("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...t})}function kr({className:e,...n}){return i.jsx(dr,{"data-slot":"dropdown-menu-separator",className:W("bg-border -mx-1 my-1 h-px",e),...n})}function Lr({className:e,...n}){return i.jsx("span",{"data-slot":"dropdown-menu-shortcut",className:W("text-muted-foreground ml-auto text-xs tracking-widest",e),...n})}export{An as A,Tr as B,On as C,Ir as D,jr as E,Po as G,Hn as I,Ro as L,Eo as P,Sn as R,No as S,Dr as a,Sr as b,Or as c,Nr as d,We as e,Nn as f,Be as g,Ar as h,kr as i,Fr as j,Lr as k,tt as l,Wn as m,it as n,yo as o,bo as p,_o as q,So as r,Ao as s,Oo as t,Io as u,Do as v,To as w,jo as x,Fo as y,hn as z};
