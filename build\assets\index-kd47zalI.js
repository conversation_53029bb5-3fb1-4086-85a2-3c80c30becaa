import{F as _,j as w,o as e,R as p}from"./index-DTZYRUcV.js";import{a as v,g as z,b as F,d as O,e as D}from"./index-CzvqTnBS.js";import{c}from"./index-Mx6w-wA8.js";import{b as G}from"./button-D3EwPaXF.js";import{C as H}from"./chevron-right-CdatEdaD.js";import{E as L}from"./ellipsis-k69hbTZf.js";/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]],B=_("chevron-left",A);/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function y(t,a){return t?X(t)?w.createElement(t,a):t:null}function X(t){return q(t)||typeof t=="function"||J(t)}function q(t){return typeof t=="function"&&(()=>{const a=Object.getPrototypeOf(t);return a.prototype&&a.prototype.isReactComponent})()}function J(t){return typeof t=="object"&&typeof t.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(t.$$typeof.description)}function K(t){const a={state:{},onStateChange:()=>{},renderFallbackValue:null,...t},[n]=w.useState(()=>({current:v(a)})),[x,b]=w.useState(()=>n.current.initialState);return n.current.setOptions(j=>({...j,...t,state:{...x,...t.state},onStateChange:h=>{b(h),t.onStateChange==null||t.onStateChange(h)}})),n.current}function Q({className:t,...a}){return e.jsx("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:e.jsx("table",{"data-slot":"table",className:c("w-full caption-bottom text-sm",t),...a})})}function U({className:t,...a}){return e.jsx("thead",{"data-slot":"table-header",className:c("[&_tr]:border-b",t),...a})}function W({className:t,...a}){return e.jsx("tbody",{"data-slot":"table-body",className:c("[&_tr:last-child]:border-0",t),...a})}function S({className:t,...a}){return e.jsx("tr",{"data-slot":"table-row",className:c("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function Y({className:t,...a}){return e.jsx("th",{"data-slot":"table-head",className:c("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function C({className:t,...a}){return e.jsx("td",{"data-slot":"table-cell",className:c("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function Z({className:t,...a}){return e.jsx("nav",{role:"navigation","aria-label":"pagination","data-slot":"pagination",className:c("mx-auto flex w-full justify-center",t),...a})}function ee({className:t,...a}){return e.jsx("ul",{"data-slot":"pagination-content",className:c("flex flex-row items-center gap-1",t),...a})}function g({...t}){return e.jsx("li",{"data-slot":"pagination-item",...t})}function m({className:t,isActive:a,size:n="icon",...x}){return e.jsx("a",{"aria-current":a?"page":void 0,"data-slot":"pagination-link","data-active":a,className:c(G({variant:a?"outline":"ghost",size:n}),t),...x})}function R({className:t,...a}){return e.jsxs(m,{"aria-label":"Go to previous page",size:"default",className:c("gap-1 px-2.5 sm:pl-2.5",t),...a,children:[e.jsx(B,{}),e.jsx("span",{className:"hidden sm:block",children:"上一页"})]})}function N({className:t,...a}){return e.jsxs(m,{"aria-label":"Go to next page",size:"default",className:c("gap-1 px-2.5 sm:pr-2.5",t),...a,children:[e.jsx("span",{className:"hidden sm:block",children:"下一页"}),e.jsx(H,{})]})}function te({className:t,...a}){return e.jsxs("span",{"aria-hidden":!0,"data-slot":"pagination-ellipsis",className:c("flex size-9 items-center justify-center",t),...a,children:[e.jsx(L,{className:"size-4"}),e.jsx("span",{className:"sr-only",children:"More pages"})]})}function ce({columns:t,data:a,serverPagination:n,enablePagination:x=!1,onRowSelectionChange:b,rowSelection:j}){const[h,P]=p.useState({}),[M,k]=p.useState([]),[E,T]=p.useState({}),I=p.useMemo(()=>(a||[]).map((s,l)=>!s||typeof s!="object"?(console.warn(`SakuraTable: 此表格数据可能没有id字段 ${l}:`,s),{id:`回调-${l}`,...s}):s.id?s:(console.warn(`SakuraTable: 数据项 ${l} 缺少id字段:`,s),{id:`生成-${l}`,...s})),[a]),V=j??E,$=b??T,d=K({data:I,columns:t||[],manualPagination:!1,getRowId:o=>String(o.id),getSubRows:o=>o.children,getCoreRowModel:D(),onSortingChange:k,getSortedRowModel:O(),getPaginationRowModel:F(),getExpandedRowModel:z(),onColumnVisibilityChange:P,enableRowSelection:!0,enableMultiRowSelection:!0,onRowSelectionChange:$,initialState:{pagination:{pageSize:Number.MAX_SAFE_INTEGER}},state:{sorting:M,columnVisibility:h,rowSelection:V}});return e.jsxs("div",{className:"flex flex-col",children:[e.jsx("div",{className:"overflow-hidden rounded-md border",children:e.jsxs(Q,{children:[e.jsx(U,{children:d.getHeaderGroups().map(o=>e.jsx(S,{children:o.headers.map(s=>e.jsx(Y,{children:s.isPlaceholder?null:y(s.column.columnDef.header,s.getContext())},s.id))},o.id))}),e.jsx(W,{children:d.getRowModel().rows?.length?d.getRowModel().rows.map(o=>e.jsx(S,{"data-state":o.getIsSelected&&o.getIsSelected()?"selected":"unselected",children:o.getVisibleCells().map(s=>e.jsx(C,{children:y(s.column.columnDef.cell,s.getContext())},s.id))},o.id)):e.jsx(S,{children:e.jsx(C,{colSpan:t?.length||1,className:"h-24 text-center",children:"没有结果"})})})]})}),e.jsx("div",{className:"flex items-center space-x-2 py-4 justify-center",children:x&&e.jsx(Z,{children:e.jsx(ee,{children:n?e.jsxs(e.Fragment,{children:[e.jsx(g,{onClick:()=>{n.currentPage>1&&n.setPage(n.currentPage-1)},children:e.jsx(R,{href:"#",className:n.currentPage<=1?"pointer-events-none opacity-50":""})}),(()=>{const o=n.totalPages,s=n.currentPage,l=10;let i=Math.max(1,s-Math.floor(l/2));const u=Math.min(o,i+l-1);u-i+1<l&&(i=Math.max(1,u-l+1));const f=[];for(let r=i;r<=u;r++)f.push(e.jsx(g,{children:e.jsx(m,{href:"#",onClick:()=>n.setPage&&n.setPage(r),className:r===s?"bg-primary text-primary-foreground":"",children:r})},r));return f})(),e.jsx(g,{onClick:()=>{n.currentPage<n.totalPages&&n.setPage&&n.setPage(n.currentPage+1)},children:e.jsx(N,{href:"#",className:n.currentPage>=n.totalPages?"pointer-events-none opacity-50":""})})]}):e.jsxs(e.Fragment,{children:[e.jsx(g,{onClick:()=>d.previousPage(),children:e.jsx(R,{href:"#"})}),(()=>{const o=d.getPageCount(),s=d.getState().pagination.pageIndex,l=10;let i=Math.max(0,s-Math.floor(l/2));const u=Math.min(o-1,i+l-1);u-i+1<l&&(i=Math.max(0,u-l+1));const f=[];for(let r=i;r<=u;r++)f.push(e.jsx(g,{children:e.jsx(m,{onClick:()=>d.setPageIndex(r),children:r+1})},r));return f})(),e.jsx(g,{children:e.jsx(te,{})}),e.jsx(g,{onClick:()=>d.nextPage(),children:e.jsx(N,{href:"#"})})]})})})})]})}export{ce as S};
