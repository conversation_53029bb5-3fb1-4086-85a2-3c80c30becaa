import{o as s}from"./index-Cq3zfZON.js";import{B as d}from"./button-CTQLK12e.js";import{D as o,a as c,b as l,c as r,i}from"./dropdown-menu-BSc-dlG8.js";import{B as n}from"./badge-BWAn-Pqt.js";import{D as t}from"./dynamic-banner-el9Dlhio.js";import{A as h}from"./arrow-up-down-CAw8WvWn.js";import{E as m}from"./ellipsis-8VrbSk0P.js";const g=[{accessorKey:"id",header:"ID",cell:({row:e})=>s.jsx("span",{className:"font-mono",children:e.getValue("id")})},{accessorKey:"name",header:({column:e})=>s.jsxs(d,{variant:"ghost",onClick:()=>e.toggleSorting(e.getIsSorted()==="asc"),children:["名称",s.jsx(h,{className:"ml-2 h-4 w-4"})]})},{accessorKey:"icon",header:"图标",cell:({row:e})=>s.jsx("div",{className:"w-10 h-10",children:s.jsx("img",{src:e.getValue("icon"),alt:"Server icon",width:40,height:40,className:"rounded"})})},{accessorKey:"banner",header:"横幅",minSize:200,maxSize:300,cell:({row:e})=>s.jsx(t,{url:e.getValue("banner")})},{accessorKey:"version",header:"版本",cell:({row:e})=>s.jsx(n,{variant:"outline",children:e.getValue("version")})},{accessorKey:"backendType",header:"核心",cell:({row:e})=>s.jsx(n,{variant:"secondary",children:e.getValue("backendType")})},{accessorKey:"edition",header:"平台",cell:({row:e})=>s.jsx(n,{variant:e.getValue("edition")==="java"?"default":"destructive",children:e.getValue("edition").toUpperCase()})},{accessorKey:"gamemodes",header:"玩法类型",cell:({row:e})=>s.jsx(n,{variant:"outline",children:e.getValue("gamemodes")})},{accessorKey:"status",header:"状态",cell:({row:e})=>{const a=e.original.status;return a?s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:`h-2 w-2 rounded-full ${a.isRuning?"bg-green-500":"bg-red-500"}`}),s.jsx("span",{children:a.isRuning?`${a.players}/${a.maxPlayers} 在线`:"离线"})]}):s.jsx("span",{className:"text-muted-foreground",children:"Unknown"})}},{accessorKey:"javaAddress",header:"java地址",cell:({row:e})=>e.getValue("javaAddress")?s.jsxs("span",{className:"font-mono",children:[e.getValue("javaAddress"),":",e.original.javaAddressPort]}):s.jsx("span",{className:"text-muted-foreground",children:"N/A"})},{accessorKey:"bedrockAddress",header:"基岩地址",cell:({row:e})=>e.getValue("bedrockAddress")?s.jsxs("span",{className:"font-mono",children:[e.getValue("bedrockAddress"),":",e.original.bedrockAddressPort]}):s.jsx("span",{className:"text-muted-foreground",children:"N/A"})},{accessorKey:"arichiveDate",header:"收录时间",cell:({row:e})=>s.jsx("span",{children:new Date(e.getValue("arichiveDate")).toLocaleDateString()})},{id:"actions",cell:({row:e})=>{const a=e.original;return s.jsxs(o,{children:[s.jsx(c,{asChild:!0,children:s.jsx(d,{variant:"ghost",className:"h-8 w-8 p-0",children:s.jsx(m,{className:"h-4 w-4"})})}),s.jsxs(l,{align:"end",children:[s.jsx(r,{onClick:()=>navigator.clipboard.writeText(a.id),children:"复制 服务器 ID"}),a.javaAddress&&s.jsx(r,{onClick:()=>navigator.clipboard.writeText(`${a.javaAddress}:${a.javaAddressPort}`),children:"复制 Java 地址"}),a.bedrockAddress&&s.jsx(r,{onClick:()=>navigator.clipboard.writeText(`${a.bedrockAddress}:${a.bedrockAddressPort}`),children:"复制 基岩 地址"}),s.jsx(i,{}),s.jsx(r,{children:"详细面板"}),s.jsx(r,{children:"编辑"}),s.jsx(r,{className:"text-red-600",children:"删除"})]})]})}}],f=Object.freeze(Object.defineProperty({__proto__:null,columns:g},Symbol.toStringTag,{value:"Module"}));export{f as _,g as c};
