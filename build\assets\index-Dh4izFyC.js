import{j as m,u as I,o as h}from"./index-DTZYRUcV.js";import{S as j}from"./index-kd47zalI.js";import{C as x}from"./colums-CPNPr5k6.js";import{r as C,d as D,e as _,f as y,t as g}from"./index-7CsvrnxQ.js";import{S as v}from"./sakura-table-bar-CwSUsw2I.js";import{R as T}from"./dialog-BlQMfYTP.js";const q=async(r=1,e)=>{try{const o={page:r,name:e};return await C.get({url:`${D.role.getRoleList}`,params:o})}catch(o){throw console.error("Product API error:",o),o}},F=async r=>{try{return await C.delete({url:`${D.role.deleteRole}/${r}`})}catch(e){throw console.error("Product API error:",e),e}},L=async({id:r,role:e})=>{try{return await C.put({url:`${D.role.updateRole}/${r}`,data:e})}catch(o){throw console.error("Product API error:",o),o}},N=async r=>{try{return await C.post({url:`${D.role.createRole}`,data:r})}catch(e){throw console.error("Product API error:",e),e}},E=()=>{const[r,e]=m.useState(1),[o,c]=m.useState(""),l=I(),i=_({queryKey:["role-list",r,o],queryFn:async()=>{try{const t=await q(r,o);return t&&t.code===200&&t.data?t.data:{roles:[],currentPage:1,size:10,totalCount:0,totalPages:1}}catch(t){console.error("API Error:",t)}},retry:3,staleTime:10*1e3,placeholderData:t=>t}),u=y({mutationFn:F,onSuccess:()=>{l.invalidateQueries({queryKey:["role-list"]}),g("删除用户成功",{description:`您在 ${new Date().toUTCString()} 删除了用户`,action:{label:"确认",onClick:()=>console.log("用户已经确认")}})},onError:t=>{g(`删除用户失败 ${t.message}`)}}),p=y({mutationFn:N,onSuccess:()=>{l.invalidateQueries({queryKey:["role-list"]}),g("创建用户成功",{description:`您在 ${new Date().toUTCString()} 创建了用户`,action:{label:"确认",onClick:()=>console.log("用户已经确认")}})},onError:t=>{g(`创建用户失败 ${t.message}`)}}),d=y({mutationFn:L,onSuccess:()=>{l.invalidateQueries({queryKey:["role-list"]}),g("更新用户成功",{description:`您在 ${new Date().toUTCString()} 更新了用户`,action:{label:"确认",onClick:()=>console.log("用户已经确认")}})},onError:t=>{g(`更新用户失败 ${t.message}`)}}),n={roles:[],currentPage:1,size:10,totalCount:0,totalPages:1},s=i.data||n;return{roleListData:s,isLoading:i.isLoading,setFilterName:c,filterName:o,error:i.error,pagination:{totalCount:Number(s.totalCount)||0,totalPages:Number(s.totalPages)||1,currentPage:Number(s.currentPage)||1,setPage:e,getPage:r},isFeching:i.isFetching,createRole:p,updateRole:d,deleteRole:u}},k=()=>{const{updateRole:r,deleteRole:e,createRole:o}=E(),[c,l]=m.useState(!1),[i,u]=m.useState(!1),[p,d]=m.useState(void 0),[n,s]=m.useState({}),t=()=>{const a=Object.keys(n).filter($=>n[$]);e.mutate(a),s({})},S=Object.keys(n).filter(a=>n[a]).length;return{handleCreate:a=>{o.mutate(a)},handleDelete:a=>{e.mutate([a])},handleEdit:a=>{r.mutate({id:a.id,role:a})},handleOpenEditDialog:a=>{d(a),u(!0)},handleCloseDialog:()=>{l(!1),u(!1),d(void 0)},handleOpenCreateDialog:()=>{l(!0)},handleDeleteSelected:t,setRowSelection:s,rowSelection:n,selectedCount:S,isCreateDialogOpen:c,isEditDialogOpen:i,editingItem:p}};function A(){const{roleListData:r,pagination:e,filterName:o,setFilterName:c}=E(),{handleCreate:l,handleEdit:i,handleDelete:u,handleOpenCreateDialog:p,handleOpenEditDialog:d,handleCloseDialog:n,handleDeleteSelected:s,rowSelection:t,selectedCount:S,setRowSelection:P,isCreateDialogOpen:R,isEditDialogOpen:f,editingItem:b}=k(),w=x({handleOpenEditDialog:d,handleDelete:u});return h.jsxs("div",{className:"container mx-auto",children:[h.jsx(v,{enableSearch:!0,enableCreate:!0,enableSelected:!0,searchValue:o,selectCount:S,onSearchChange:c,onOpenCreateDialog:p,onDeleteItems:s,searchPlaceholder:"搜索名称",createButtonText:"创建用户"}),h.jsx(j,{columns:w,data:r?.roles.sort((O,a)=>O.order-a.order)||[],serverPagination:e,rowSelection:t,enablePagination:!0,onRowSelectionChange:P}),h.jsx(T,{open:R||f,enableSelected:!0,updateItem:b,handleCreate:l,handleEdit:i,onClose:n})]})}const V=Object.freeze(Object.defineProperty({__proto__:null,default:A},Symbol.toStringTag,{value:"Module"}));export{V as _};
