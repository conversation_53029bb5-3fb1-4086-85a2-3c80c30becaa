import{o as e}from"./index-DTZYRUcV.js";import{S as s,A as r,a as i,b as d,B as l,c,d as a,f as t,g as n,e as o}from"./breadcrumb-BFM03i4T.js";import{S as m}from"./use-system-CdUfL77p.js";function x(){return e.jsxs(s,{children:[e.jsx(r,{}),e.jsxs(i,{children:[e.jsx("header",{className:"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12",children:e.jsxs("div",{className:"flex items-center gap-2 px-4",children:[e.jsx(d,{className:"-ml-1"}),e.jsx(m,{orientation:"vertical",className:"mr-2 data-[orientation=vertical]:h-4"}),e.jsx(l,{children:e.jsxs(c,{children:[e.jsx(a,{className:"hidden md:block",children:e.jsx(t,{href:"#",children:"Building Your Application"})}),e.jsx(n,{className:"hidden md:block"}),e.jsx(a,{children:e.jsx(o,{children:"Data Fetching"})})]})})]})}),e.jsxs("div",{className:"flex flex-1 flex-col gap-4 p-4 pt-0",children:[e.jsxs("div",{className:"grid auto-rows-min gap-4 md:grid-cols-3",children:[e.jsx("div",{className:"bg-muted/50 aspect-video rounded-xl"}),e.jsx("div",{className:"bg-muted/50 aspect-video rounded-xl"}),e.jsx("div",{className:"bg-muted/50 aspect-video rounded-xl"})]}),e.jsx("div",{className:"bg-muted/50 min-h-[100vh] flex-1 rounded-xl md:min-h-min"})]})]})]})}const b=Object.freeze(Object.defineProperty({__proto__:null,default:x},Symbol.toStringTag,{value:"Module"}));export{b as _};
