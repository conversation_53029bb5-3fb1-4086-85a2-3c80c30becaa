import{o as e}from"./index-Cq3zfZON.js";import{B as s}from"./badge-BWAn-Pqt.js";function t(){return e.jsxs("div",{className:"text-primary text-3xl mt-50 w-full container flex flex-col justify-center items-center font-bold",children:[e.jsx("h1",{children:"Sakura-Admin"}),e.jsx("h1",{className:"text-xl italic",children:"Version@Dev 0.1.2"}),e.jsx("h1",{className:"text-xl font-mono",children:"<EMAIL>"}),e.jsxs("div",{className:"space-x-2",children:[e.jsx(s,{className:"text-foreground bg-secondary",children:"react"}),e.jsx(s,{className:"text-primary bg-secondary",children:"shadcn"}),e.jsx(s,{className:"text-primary bg-secondary",children:"tailwind css"}),e.jsx(s,{className:"text-primary bg-secondary",children:"zustand"}),e.jsx(s,{className:"text-primary bg-secondary",children:"tanstack"})]})]})}const c=Object.freeze(Object.defineProperty({__proto__:null,default:t},Symbol.toStringTag,{value:"Module"}));export{c as _};
