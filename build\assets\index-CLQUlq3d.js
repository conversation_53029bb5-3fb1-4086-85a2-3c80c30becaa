const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-BCXtus2y.js","assets/index-Cq3zfZON.js","assets/index-3lwQEGmT.css","assets/index-CiJpZiBe.js","assets/permissiom-select-tree-BHeUl-vp.js","assets/button-CTQLK12e.js","assets/index-CGKXfO_7.js","assets/scroll-area-DSIcPaiT.js","assets/index-VR2XJ8FE.js","assets/index-anJos_f1.js","assets/index-BgXqfL-L.js","assets/index-5UnhShgU.js","assets/index-Dc_FVRD7.js","assets/index-Dl90smRv.js","assets/index-MsHvSdph.js","assets/index-CnargNwx.js","assets/checkbox-D_eY1Ew7.js","assets/index-DyHSLvub.js","assets/index-C2Z7v5r4.js","assets/check-BfG1ovsW.js","assets/chevron-right-DfqRsWu5.js","assets/server-card-playground-CXBsE500.js","assets/minecraft-server-types-Y8oP8MH7.js","assets/card-B51fHEQ3.js","assets/progress-BNQRkllt.js","assets/dynamic-banner-el9Dlhio.js","assets/badge-BWAn-Pqt.js","assets/badge-check-CQyl-k7O.js","assets/border-loading-playground-VhUkW4w5.js","assets/index-CoWXcXUt.js","assets/index-DCtNtNWe.js","assets/index-uxbcQK2J.js","assets/index-CYDP3JK6.js","assets/system-setting-BCL-5juB.js","assets/sheet-CX8oba17.js","assets/index-D-ENlbqy.js","assets/index-erjOU2F6.js","assets/x-Cff2Ll5j.js","assets/icon-DWEEtME9.js","assets/use-system-DaYsginW.js","assets/label-BYGxJBkR.js","assets/switch-qzGntkBt.js","assets/system-tabs-DoErleGg.js","assets/use-tabs-D9RsThBN.js","assets/index-CsW3xZnI.js","assets/breadcrumb-BTu2HnNQ.js","assets/dropdown-menu-BSc-dlG8.js","assets/index-BBWlQkPG.js","assets/index-fbjW5b5I.js","assets/chevrons-up-down-BhFlrKDQ.js","assets/plus-MYQztgf7.js","assets/index-BRJbtU0g.js","assets/login-form-6hze7DT4.js","assets/form-BGpNDXKP.js","assets/input-BHGiuYUZ.js","assets/index-dShf-qZ2.js","assets/column-CNF2cE_S.js","assets/index-CzvqTnBS.js","assets/ellipsis-8VrbSk0P.js","assets/move-down-B2d3LfE9.js","assets/auto-complete-box-kbhyGzIw.js","assets/select-parent-box-DUuLIPxS.js","assets/dialog-Ch8HczLc.js","assets/dialog-DSOM4rc6.js","assets/index-C-scFdsV.js","assets/sakura-table-bar-DOjbc8KC.js","assets/colums-B5o1wkEj.js","assets/index-CsShudox.js","assets/index-CbSxiwz2.js","assets/columns-Bks_ghfS.js","assets/arrow-up-down-CAw8WvWn.js","assets/index-BqVn8FSZ.js","assets/mock-server-list-CdTKvwmG.js","assets/columns-_UrUPpXo.js","assets/index-C-3UUo7c.js","assets/index-DXFeEy7g.js","assets/columns-QUGf2FQe.js","assets/index-Fk45LKSJ.js","assets/test-icon-select-panel-CM8B11xS.js","assets/index-Cq0RLz-l.js","assets/index-DDkN25NJ.js","assets/colums-DZo9Badn.js","assets/dialog-DhF9uMsq.js","assets/index-CQdrasTd.js","assets/index-DYEGkPY0.js"])))=>i.map(i=>d[i]);
import{S as ya,p as io,r as Be,s as Dr,a as Wt,n as Ar,i as vn,b as lo,t as Js,f as Qs,c as Ys,d as uo,e as Br,h as co,g as Xs,j as g,k as va,u as Fn,l as xt,R as P,m as Gs,o as he,q as Zs,_ as V,v as ei}from"./index-Cq3zfZON.js";var ti=class extends ya{constructor(e,t){super(),this.options=t,this.#r=e,this.#i=null,this.#s=io(),this.options.experimental_prefetchInRender||this.#s.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#r;#e=void 0;#n=void 0;#t=void 0;#o;#a;#s;#i;#m;#f;#h;#u;#c;#l;#p=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(this.#e.addObserver(this),fo(this.#e,this.options)?this.#d():this.updateResult(),this.#b())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return bn(this.#e,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return bn(this.#e,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#w(),this.#E(),this.#e.removeObserver(this)}setOptions(e){const t=this.options,r=this.#e;if(this.options=this.#r.defaultQueryOptions(e),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof Be(this.options.enabled,this.#e)!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#x(),this.#e.setOptions(this.options),t._defaulted&&!Dr(this.options,t)&&this.#r.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#e,observer:this});const n=this.hasListeners();n&&ho(this.#e,r,this.options,t)&&this.#d(),this.updateResult(),n&&(this.#e!==r||Be(this.options.enabled,this.#e)!==Be(t.enabled,this.#e)||Wt(this.options.staleTime,this.#e)!==Wt(t.staleTime,this.#e))&&this.#g();const o=this.#y();n&&(this.#e!==r||Be(this.options.enabled,this.#e)!==Be(t.enabled,this.#e)||o!==this.#l)&&this.#v(o)}getOptimisticResult(e){const t=this.#r.getQueryCache().build(this.#r,e),r=this.createResult(t,e);return ni(this,r)&&(this.#t=r,this.#a=this.options,this.#o=this.#e.state),r}getCurrentResult(){return this.#t}trackResult(e,t){return new Proxy(e,{get:(r,n)=>(this.trackProp(n),t?.(n),Reflect.get(r,n))})}trackProp(e){this.#p.add(e)}getCurrentQuery(){return this.#e}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const t=this.#r.defaultQueryOptions(e),r=this.#r.getQueryCache().build(this.#r,t);return r.fetch().then(()=>this.createResult(r,t))}fetch(e){return this.#d({...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#t))}#d(e){this.#x();let t=this.#e.fetch(this.options,e);return e?.throwOnError||(t=t.catch(Ar)),t}#g(){this.#w();const e=Wt(this.options.staleTime,this.#e);if(vn||this.#t.isStale||!lo(e))return;const r=Js(this.#t.dataUpdatedAt,e)+1;this.#u=setTimeout(()=>{this.#t.isStale||this.updateResult()},r)}#y(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.#e):this.options.refetchInterval)??!1}#v(e){this.#E(),this.#l=e,!(vn||Be(this.options.enabled,this.#e)===!1||!lo(this.#l)||this.#l===0)&&(this.#c=setInterval(()=>{(this.options.refetchIntervalInBackground||Qs.isFocused())&&this.#d()},this.#l))}#b(){this.#g(),this.#v(this.#y())}#w(){this.#u&&(clearTimeout(this.#u),this.#u=void 0)}#E(){this.#c&&(clearInterval(this.#c),this.#c=void 0)}createResult(e,t){const r=this.#e,n=this.options,o=this.#t,a=this.#o,s=this.#a,i=e!==r?e.state:this.#n,{state:u}=e;let d={...u},c=!1,h;if(t._optimisticResults){const j=this.hasListeners(),B=!j&&fo(e,t),K=j&&ho(e,r,t,n);(B||K)&&(d={...d,...Ys(u.data,e.options)}),t._optimisticResults==="isRestoring"&&(d.fetchStatus="idle")}let{error:b,errorUpdatedAt:v,status:R}=d;h=d.data;let m=!1;if(t.placeholderData!==void 0&&h===void 0&&R==="pending"){let j;o?.isPlaceholderData&&t.placeholderData===s?.placeholderData?(j=o.data,m=!0):j=typeof t.placeholderData=="function"?t.placeholderData(this.#h?.state.data,this.#h):t.placeholderData,j!==void 0&&(R="success",h=uo(o?.data,j,t),c=!0)}if(t.select&&h!==void 0&&!m)if(o&&h===a?.data&&t.select===this.#m)h=this.#f;else try{this.#m=t.select,h=t.select(h),h=uo(o?.data,h,t),this.#f=h,this.#i=null}catch(j){this.#i=j}this.#i&&(b=this.#i,h=this.#f,v=Date.now(),R="error");const w=d.fetchStatus==="fetching",C=R==="pending",S=R==="error",A=C&&w,D=h!==void 0,k={status:R,fetchStatus:d.fetchStatus,isPending:C,isSuccess:R==="success",isError:S,isInitialLoading:A,isLoading:A,data:h,dataUpdatedAt:d.dataUpdatedAt,error:b,errorUpdatedAt:v,failureCount:d.fetchFailureCount,failureReason:d.fetchFailureReason,errorUpdateCount:d.errorUpdateCount,isFetched:d.dataUpdateCount>0||d.errorUpdateCount>0,isFetchedAfterMount:d.dataUpdateCount>i.dataUpdateCount||d.errorUpdateCount>i.errorUpdateCount,isFetching:w,isRefetching:w&&!C,isLoadingError:S&&!D,isPaused:d.fetchStatus==="paused",isPlaceholderData:c,isRefetchError:S&&D,isStale:Un(e,t),refetch:this.refetch,promise:this.#s,isEnabled:Be(t.enabled,e)!==!1};if(this.options.experimental_prefetchInRender){const j=te=>{k.status==="error"?te.reject(k.error):k.data!==void 0&&te.resolve(k.data)},B=()=>{const te=this.#s=k.promise=io();j(te)},K=this.#s;switch(K.status){case"pending":e.queryHash===r.queryHash&&j(K);break;case"fulfilled":(k.status==="error"||k.data!==K.value)&&B();break;case"rejected":(k.status!=="error"||k.error!==K.reason)&&B();break}}return k}updateResult(){const e=this.#t,t=this.createResult(this.#e,this.options);if(this.#o=this.#e.state,this.#a=this.options,this.#o.data!==void 0&&(this.#h=this.#e),Dr(t,e))return;this.#t=t;const r=()=>{if(!e)return!0;const{notifyOnChangeProps:n}=this.options,o=typeof n=="function"?n():n;if(o==="all"||!o&&!this.#p.size)return!0;const a=new Set(o??this.#p);return this.options.throwOnError&&a.add("error"),Object.keys(this.#t).some(s=>{const l=s;return this.#t[l]!==e[l]&&a.has(l)})};this.#R({listeners:r()})}#x(){const e=this.#r.getQueryCache().build(this.#r,this.options);if(e===this.#e)return;const t=this.#e;this.#e=e,this.#n=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#b()}#R(e){Br.batch(()=>{e.listeners&&this.listeners.forEach(t=>{t(this.#t)}),this.#r.getQueryCache().notify({query:this.#e,type:"observerResultsUpdated"})})}};function ri(e,t){return Be(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function fo(e,t){return ri(e,t)||e.state.data!==void 0&&bn(e,t,t.refetchOnMount)}function bn(e,t,r){if(Be(t.enabled,e)!==!1&&Wt(t.staleTime,e)!=="static"){const n=typeof r=="function"?r(e):r;return n==="always"||n!==!1&&Un(e,t)}return!1}function ho(e,t,r,n){return(e!==t||Be(n.enabled,e)===!1)&&(!r.suspense||e.state.status!=="error")&&Un(e,r)}function Un(e,t){return Be(t.enabled,e)!==!1&&e.isStaleByTime(Wt(t.staleTime,e))}function ni(e,t){return!Dr(e.getCurrentResult(),t)}var oi=class extends ya{#r;#e=void 0;#n;#t;constructor(t,r){super(),this.#r=t,this.setOptions(r),this.bindMethods(),this.#o()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){const r=this.options;this.options=this.#r.defaultMutationOptions(t),Dr(this.options,r)||this.#r.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#n,observer:this}),r?.mutationKey&&this.options.mutationKey&&co(r.mutationKey)!==co(this.options.mutationKey)?this.reset():this.#n?.state.status==="pending"&&this.#n.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#n?.removeObserver(this)}onMutationUpdate(t){this.#o(),this.#a(t)}getCurrentResult(){return this.#e}reset(){this.#n?.removeObserver(this),this.#n=void 0,this.#o(),this.#a()}mutate(t,r){return this.#t=r,this.#n?.removeObserver(this),this.#n=this.#r.getMutationCache().build(this.#r,this.options),this.#n.addObserver(this),this.#n.execute(t)}#o(){const t=this.#n?.state??Xs();this.#e={...t,isPending:t.status==="pending",isSuccess:t.status==="success",isError:t.status==="error",isIdle:t.status==="idle",mutate:this.mutate,reset:this.reset}}#a(t){Br.batch(()=>{if(this.#t&&this.hasListeners()){const r=this.#e.variables,n=this.#e.context;t?.type==="success"?(this.#t.onSuccess?.(t.data,r,n),this.#t.onSettled?.(t.data,null,r,n)):t?.type==="error"&&(this.#t.onError?.(t.error,r,n),this.#t.onSettled?.(void 0,t.error,r,n))}this.listeners.forEach(r=>{r(this.#e)})})}},ba=g.createContext(!1),ai=()=>g.useContext(ba);ba.Provider;function si(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var ii=g.createContext(si()),li=()=>g.useContext(ii),ui=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},ci=e=>{g.useEffect(()=>{e.clearReset()},[e])},di=({result:e,errorResetBoundary:t,throwOnError:r,query:n,suspense:o})=>e.isError&&!t.isReset()&&!e.isFetching&&n&&(o&&e.data===void 0||va(r,[e.error,n])),fi=e=>{if(e.suspense){const t=n=>n==="static"?n:Math.max(n??1e3,1e3),r=e.staleTime;e.staleTime=typeof r=="function"?(...n)=>t(r(...n)):t(r),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3))}},hi=(e,t)=>e.isLoading&&e.isFetching&&!t,pi=(e,t)=>e?.suspense&&t.isPending,po=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function mi(e,t,r){const n=ai(),o=li(),a=Fn(),s=a.defaultQueryOptions(e);a.getDefaultOptions().queries?._experimental_beforeQuery?.(s),s._optimisticResults=n?"isRestoring":"optimistic",fi(s),ui(s,o),ci(o);const l=!a.getQueryCache().get(s.queryHash),[i]=g.useState(()=>new t(a,s)),u=i.getOptimisticResult(s),d=!n&&e.subscribed!==!1;if(g.useSyncExternalStore(g.useCallback(c=>{const h=d?i.subscribe(Br.batchCalls(c)):Ar;return i.updateResult(),h},[i,d]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),g.useEffect(()=>{i.setOptions(s)},[s,i]),pi(s,u))throw po(s,i,o);if(di({result:u,errorResetBoundary:o,throwOnError:s.throwOnError,query:a.getQueryCache().get(s.queryHash),suspense:s.suspense}))throw u.error;return a.getDefaultOptions().queries?._experimental_afterQuery?.(s,u),s.experimental_prefetchInRender&&!vn&&hi(u,n)&&(l?po(s,i,o):a.getQueryCache().get(s.queryHash)?.promise)?.catch(Ar).finally(()=>{i.updateResult()}),s.notifyOnChangeProps?u:i.trackResult(u)}function wa(e,t){return mi(e,ti)}function Er(e,t){const r=Fn(),[n]=g.useState(()=>new oi(r,e));g.useEffect(()=>{n.setOptions(e)},[n,e]);const o=g.useSyncExternalStore(g.useCallback(s=>n.subscribe(Br.batchCalls(s)),[n]),()=>n.getCurrentResult(),()=>n.getCurrentResult()),a=g.useCallback((s,l)=>{n.mutate(s,l).catch(Ar)},[n]);if(o.error&&va(n.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:a,mutateAsync:o.mutate}}function Ea(e,t){return function(){return e.apply(t,arguments)}}const{toString:gi}=Object.prototype,{getPrototypeOf:jn}=Object,{iterator:$r,toStringTag:xa}=Symbol,zr=(e=>t=>{const r=gi.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),$e=e=>(e=e.toLowerCase(),t=>zr(t)===e),Hr=e=>t=>typeof t===e,{isArray:Tt}=Array,Jt=Hr("undefined");function Zt(e){return e!==null&&!Jt(e)&&e.constructor!==null&&!Jt(e.constructor)&&Pe(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Ra=$e("ArrayBuffer");function yi(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Ra(e.buffer),t}const vi=Hr("string"),Pe=Hr("function"),Sa=Hr("number"),er=e=>e!==null&&typeof e=="object",bi=e=>e===!0||e===!1,xr=e=>{if(zr(e)!=="object")return!1;const t=jn(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(xa in e)&&!($r in e)},wi=e=>{if(!er(e)||Zt(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},Ei=$e("Date"),xi=$e("File"),Ri=$e("Blob"),Si=$e("FileList"),_i=e=>er(e)&&Pe(e.pipe),Ti=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Pe(e.append)&&((t=zr(e))==="formdata"||t==="object"&&Pe(e.toString)&&e.toString()==="[object FormData]"))},Pi=$e("URLSearchParams"),[Ci,Oi,Li,Di]=["ReadableStream","Request","Response","Headers"].map($e),Ai=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function tr(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let n,o;if(typeof e!="object"&&(e=[e]),Tt(e))for(n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else{if(Zt(e))return;const a=r?Object.getOwnPropertyNames(e):Object.keys(e),s=a.length;let l;for(n=0;n<s;n++)l=a[n],t.call(null,e[l],l,e)}}function _a(e,t){if(Zt(e))return null;t=t.toLowerCase();const r=Object.keys(e);let n=r.length,o;for(;n-- >0;)if(o=r[n],t===o.toLowerCase())return o;return null}const ht=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Ta=e=>!Jt(e)&&e!==ht;function wn(){const{caseless:e}=Ta(this)&&this||{},t={},r=(n,o)=>{const a=e&&_a(t,o)||o;xr(t[a])&&xr(n)?t[a]=wn(t[a],n):xr(n)?t[a]=wn({},n):Tt(n)?t[a]=n.slice():t[a]=n};for(let n=0,o=arguments.length;n<o;n++)arguments[n]&&tr(arguments[n],r);return t}const Ii=(e,t,r,{allOwnKeys:n}={})=>(tr(t,(o,a)=>{r&&Pe(o)?e[a]=Ea(o,r):e[a]=o},{allOwnKeys:n}),e),ki=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Mi=(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},Ni=(e,t,r,n)=>{let o,a,s;const l={};if(t=t||{},e==null)return t;do{for(o=Object.getOwnPropertyNames(e),a=o.length;a-- >0;)s=o[a],(!n||n(s,e,t))&&!l[s]&&(t[s]=e[s],l[s]=!0);e=r!==!1&&jn(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},Fi=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return n!==-1&&n===r},Ui=e=>{if(!e)return null;if(Tt(e))return e;let t=e.length;if(!Sa(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},ji=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&jn(Uint8Array)),Bi=(e,t)=>{const n=(e&&e[$r]).call(e);let o;for(;(o=n.next())&&!o.done;){const a=o.value;t.call(e,a[0],a[1])}},$i=(e,t)=>{let r;const n=[];for(;(r=e.exec(t))!==null;)n.push(r);return n},zi=$e("HTMLFormElement"),Hi=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,o){return n.toUpperCase()+o}),mo=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),Vi=$e("RegExp"),Pa=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};tr(r,(o,a)=>{let s;(s=t(o,a,e))!==!1&&(n[a]=s||o)}),Object.defineProperties(e,n)},qi=e=>{Pa(e,(t,r)=>{if(Pe(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=e[r];if(Pe(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Wi=(e,t)=>{const r={},n=o=>{o.forEach(a=>{r[a]=!0})};return Tt(e)?n(e):n(String(e).split(t)),r},Ki=()=>{},Ji=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Qi(e){return!!(e&&Pe(e.append)&&e[xa]==="FormData"&&e[$r])}const Yi=e=>{const t=new Array(10),r=(n,o)=>{if(er(n)){if(t.indexOf(n)>=0)return;if(Zt(n))return n;if(!("toJSON"in n)){t[o]=n;const a=Tt(n)?[]:{};return tr(n,(s,l)=>{const i=r(s,o+1);!Jt(i)&&(a[l]=i)}),t[o]=void 0,a}}return n};return r(e,0)},Xi=$e("AsyncFunction"),Gi=e=>e&&(er(e)||Pe(e))&&Pe(e.then)&&Pe(e.catch),Ca=((e,t)=>e?setImmediate:t?((r,n)=>(ht.addEventListener("message",({source:o,data:a})=>{o===ht&&a===r&&n.length&&n.shift()()},!1),o=>{n.push(o),ht.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",Pe(ht.postMessage)),Zi=typeof queueMicrotask<"u"?queueMicrotask.bind(ht):typeof process<"u"&&process.nextTick||Ca,el=e=>e!=null&&Pe(e[$r]),x={isArray:Tt,isArrayBuffer:Ra,isBuffer:Zt,isFormData:Ti,isArrayBufferView:yi,isString:vi,isNumber:Sa,isBoolean:bi,isObject:er,isPlainObject:xr,isEmptyObject:wi,isReadableStream:Ci,isRequest:Oi,isResponse:Li,isHeaders:Di,isUndefined:Jt,isDate:Ei,isFile:xi,isBlob:Ri,isRegExp:Vi,isFunction:Pe,isStream:_i,isURLSearchParams:Pi,isTypedArray:ji,isFileList:Si,forEach:tr,merge:wn,extend:Ii,trim:Ai,stripBOM:ki,inherits:Mi,toFlatObject:Ni,kindOf:zr,kindOfTest:$e,endsWith:Fi,toArray:Ui,forEachEntry:Bi,matchAll:$i,isHTMLForm:zi,hasOwnProperty:mo,hasOwnProp:mo,reduceDescriptors:Pa,freezeMethods:qi,toObjectSet:Wi,toCamelCase:Hi,noop:Ki,toFiniteNumber:Ji,findKey:_a,global:ht,isContextDefined:Ta,isSpecCompliantForm:Qi,toJSONObject:Yi,isAsyncFn:Xi,isThenable:Gi,setImmediate:Ca,asap:Zi,isIterable:el};function H(e,t,r,n,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),o&&(this.response=o,this.status=o.status?o.status:null)}x.inherits(H,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:x.toJSONObject(this.config),code:this.code,status:this.status}}});const Oa=H.prototype,La={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{La[e]={value:e}});Object.defineProperties(H,La);Object.defineProperty(Oa,"isAxiosError",{value:!0});H.from=(e,t,r,n,o,a)=>{const s=Object.create(Oa);return x.toFlatObject(e,s,function(i){return i!==Error.prototype},l=>l!=="isAxiosError"),H.call(s,e.message,t,r,n,o),s.cause=e,s.name=e.name,a&&Object.assign(s,a),s};const tl=null;function En(e){return x.isPlainObject(e)||x.isArray(e)}function Da(e){return x.endsWith(e,"[]")?e.slice(0,-2):e}function go(e,t,r){return e?e.concat(t).map(function(o,a){return o=Da(o),!r&&a?"["+o+"]":o}).join(r?".":""):t}function rl(e){return x.isArray(e)&&!e.some(En)}const nl=x.toFlatObject(x,{},null,function(t){return/^is[A-Z]/.test(t)});function Vr(e,t,r){if(!x.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=x.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(R,m){return!x.isUndefined(m[R])});const n=r.metaTokens,o=r.visitor||d,a=r.dots,s=r.indexes,i=(r.Blob||typeof Blob<"u"&&Blob)&&x.isSpecCompliantForm(t);if(!x.isFunction(o))throw new TypeError("visitor must be a function");function u(v){if(v===null)return"";if(x.isDate(v))return v.toISOString();if(x.isBoolean(v))return v.toString();if(!i&&x.isBlob(v))throw new H("Blob is not supported. Use a Buffer instead.");return x.isArrayBuffer(v)||x.isTypedArray(v)?i&&typeof Blob=="function"?new Blob([v]):Buffer.from(v):v}function d(v,R,m){let w=v;if(v&&!m&&typeof v=="object"){if(x.endsWith(R,"{}"))R=n?R:R.slice(0,-2),v=JSON.stringify(v);else if(x.isArray(v)&&rl(v)||(x.isFileList(v)||x.endsWith(R,"[]"))&&(w=x.toArray(v)))return R=Da(R),w.forEach(function(S,A){!(x.isUndefined(S)||S===null)&&t.append(s===!0?go([R],A,a):s===null?R:R+"[]",u(S))}),!1}return En(v)?!0:(t.append(go(m,R,a),u(v)),!1)}const c=[],h=Object.assign(nl,{defaultVisitor:d,convertValue:u,isVisitable:En});function b(v,R){if(!x.isUndefined(v)){if(c.indexOf(v)!==-1)throw Error("Circular reference detected in "+R.join("."));c.push(v),x.forEach(v,function(w,C){(!(x.isUndefined(w)||w===null)&&o.call(t,w,x.isString(C)?C.trim():C,R,h))===!0&&b(w,R?R.concat(C):[C])}),c.pop()}}if(!x.isObject(e))throw new TypeError("data must be an object");return b(e),t}function yo(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function Bn(e,t){this._pairs=[],e&&Vr(e,this,t)}const Aa=Bn.prototype;Aa.append=function(t,r){this._pairs.push([t,r])};Aa.toString=function(t){const r=t?function(n){return t.call(this,n,yo)}:yo;return this._pairs.map(function(o){return r(o[0])+"="+r(o[1])},"").join("&")};function ol(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ia(e,t,r){if(!t)return e;const n=r&&r.encode||ol;x.isFunction(r)&&(r={serialize:r});const o=r&&r.serialize;let a;if(o?a=o(t,r):a=x.isURLSearchParams(t)?t.toString():new Bn(t,r).toString(n),a){const s=e.indexOf("#");s!==-1&&(e=e.slice(0,s)),e+=(e.indexOf("?")===-1?"?":"&")+a}return e}class vo{constructor(){this.handlers=[]}use(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){x.forEach(this.handlers,function(n){n!==null&&t(n)})}}const ka={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},al=typeof URLSearchParams<"u"?URLSearchParams:Bn,sl=typeof FormData<"u"?FormData:null,il=typeof Blob<"u"?Blob:null,ll={isBrowser:!0,classes:{URLSearchParams:al,FormData:sl,Blob:il},protocols:["http","https","file","blob","url","data"]},$n=typeof window<"u"&&typeof document<"u",xn=typeof navigator=="object"&&navigator||void 0,ul=$n&&(!xn||["ReactNative","NativeScript","NS"].indexOf(xn.product)<0),cl=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",dl=$n&&window.location.href||"http://localhost",fl=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:$n,hasStandardBrowserEnv:ul,hasStandardBrowserWebWorkerEnv:cl,navigator:xn,origin:dl},Symbol.toStringTag,{value:"Module"})),Ee={...fl,...ll};function hl(e,t){return Vr(e,new Ee.classes.URLSearchParams,{visitor:function(r,n,o,a){return Ee.isNode&&x.isBuffer(r)?(this.append(n,r.toString("base64")),!1):a.defaultVisitor.apply(this,arguments)},...t})}function pl(e){return x.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function ml(e){const t={},r=Object.keys(e);let n;const o=r.length;let a;for(n=0;n<o;n++)a=r[n],t[a]=e[a];return t}function Ma(e){function t(r,n,o,a){let s=r[a++];if(s==="__proto__")return!0;const l=Number.isFinite(+s),i=a>=r.length;return s=!s&&x.isArray(o)?o.length:s,i?(x.hasOwnProp(o,s)?o[s]=[o[s],n]:o[s]=n,!l):((!o[s]||!x.isObject(o[s]))&&(o[s]=[]),t(r,n,o[s],a)&&x.isArray(o[s])&&(o[s]=ml(o[s])),!l)}if(x.isFormData(e)&&x.isFunction(e.entries)){const r={};return x.forEachEntry(e,(n,o)=>{t(pl(n),o,r,0)}),r}return null}function gl(e,t,r){if(x.isString(e))try{return(t||JSON.parse)(e),x.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(e)}const rr={transitional:ka,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const n=r.getContentType()||"",o=n.indexOf("application/json")>-1,a=x.isObject(t);if(a&&x.isHTMLForm(t)&&(t=new FormData(t)),x.isFormData(t))return o?JSON.stringify(Ma(t)):t;if(x.isArrayBuffer(t)||x.isBuffer(t)||x.isStream(t)||x.isFile(t)||x.isBlob(t)||x.isReadableStream(t))return t;if(x.isArrayBufferView(t))return t.buffer;if(x.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return hl(t,this.formSerializer).toString();if((l=x.isFileList(t))||n.indexOf("multipart/form-data")>-1){const i=this.env&&this.env.FormData;return Vr(l?{"files[]":t}:t,i&&new i,this.formSerializer)}}return a||o?(r.setContentType("application/json",!1),gl(t)):t}],transformResponse:[function(t){const r=this.transitional||rr.transitional,n=r&&r.forcedJSONParsing,o=this.responseType==="json";if(x.isResponse(t)||x.isReadableStream(t))return t;if(t&&x.isString(t)&&(n&&!this.responseType||o)){const s=!(r&&r.silentJSONParsing)&&o;try{return JSON.parse(t)}catch(l){if(s)throw l.name==="SyntaxError"?H.from(l,H.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ee.classes.FormData,Blob:Ee.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};x.forEach(["delete","get","head","post","put","patch"],e=>{rr.headers[e]={}});const yl=x.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),vl=e=>{const t={};let r,n,o;return e&&e.split(`
`).forEach(function(s){o=s.indexOf(":"),r=s.substring(0,o).trim().toLowerCase(),n=s.substring(o+1).trim(),!(!r||t[r]&&yl[r])&&(r==="set-cookie"?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)}),t},bo=Symbol("internals");function jt(e){return e&&String(e).trim().toLowerCase()}function Rr(e){return e===!1||e==null?e:x.isArray(e)?e.map(Rr):String(e)}function bl(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}const wl=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function an(e,t,r,n,o){if(x.isFunction(n))return n.call(this,t,r);if(o&&(t=r),!!x.isString(t)){if(x.isString(n))return t.indexOf(n)!==-1;if(x.isRegExp(n))return n.test(t)}}function El(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,n)=>r.toUpperCase()+n)}function xl(e,t){const r=x.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(o,a,s){return this[n].call(this,t,o,a,s)},configurable:!0})})}let Ce=class{constructor(t){t&&this.set(t)}set(t,r,n){const o=this;function a(l,i,u){const d=jt(i);if(!d)throw new Error("header name must be a non-empty string");const c=x.findKey(o,d);(!c||o[c]===void 0||u===!0||u===void 0&&o[c]!==!1)&&(o[c||i]=Rr(l))}const s=(l,i)=>x.forEach(l,(u,d)=>a(u,d,i));if(x.isPlainObject(t)||t instanceof this.constructor)s(t,r);else if(x.isString(t)&&(t=t.trim())&&!wl(t))s(vl(t),r);else if(x.isObject(t)&&x.isIterable(t)){let l={},i,u;for(const d of t){if(!x.isArray(d))throw TypeError("Object iterator must return a key-value pair");l[u=d[0]]=(i=l[u])?x.isArray(i)?[...i,d[1]]:[i,d[1]]:d[1]}s(l,r)}else t!=null&&a(r,t,n);return this}get(t,r){if(t=jt(t),t){const n=x.findKey(this,t);if(n){const o=this[n];if(!r)return o;if(r===!0)return bl(o);if(x.isFunction(r))return r.call(this,o,n);if(x.isRegExp(r))return r.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=jt(t),t){const n=x.findKey(this,t);return!!(n&&this[n]!==void 0&&(!r||an(this,this[n],n,r)))}return!1}delete(t,r){const n=this;let o=!1;function a(s){if(s=jt(s),s){const l=x.findKey(n,s);l&&(!r||an(n,n[l],l,r))&&(delete n[l],o=!0)}}return x.isArray(t)?t.forEach(a):a(t),o}clear(t){const r=Object.keys(this);let n=r.length,o=!1;for(;n--;){const a=r[n];(!t||an(this,this[a],a,t,!0))&&(delete this[a],o=!0)}return o}normalize(t){const r=this,n={};return x.forEach(this,(o,a)=>{const s=x.findKey(n,a);if(s){r[s]=Rr(o),delete r[a];return}const l=t?El(a):String(a).trim();l!==a&&delete r[a],r[l]=Rr(o),n[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return x.forEach(this,(n,o)=>{n!=null&&n!==!1&&(r[o]=t&&x.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const n=new this(t);return r.forEach(o=>n.set(o)),n}static accessor(t){const n=(this[bo]=this[bo]={accessors:{}}).accessors,o=this.prototype;function a(s){const l=jt(s);n[l]||(xl(o,s),n[l]=!0)}return x.isArray(t)?t.forEach(a):a(t),this}};Ce.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);x.reduceDescriptors(Ce.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[r]=n}}});x.freezeMethods(Ce);function sn(e,t){const r=this||rr,n=t||r,o=Ce.from(n.headers);let a=n.data;return x.forEach(e,function(l){a=l.call(r,a,o.normalize(),t?t.status:void 0)}),o.normalize(),a}function Na(e){return!!(e&&e.__CANCEL__)}function Pt(e,t,r){H.call(this,e??"canceled",H.ERR_CANCELED,t,r),this.name="CanceledError"}x.inherits(Pt,H,{__CANCEL__:!0});function Fa(e,t,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new H("Request failed with status code "+r.status,[H.ERR_BAD_REQUEST,H.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function Rl(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Sl(e,t){e=e||10;const r=new Array(e),n=new Array(e);let o=0,a=0,s;return t=t!==void 0?t:1e3,function(i){const u=Date.now(),d=n[a];s||(s=u),r[o]=i,n[o]=u;let c=a,h=0;for(;c!==o;)h+=r[c++],c=c%e;if(o=(o+1)%e,o===a&&(a=(a+1)%e),u-s<t)return;const b=d&&u-d;return b?Math.round(h*1e3/b):void 0}}function _l(e,t){let r=0,n=1e3/t,o,a;const s=(u,d=Date.now())=>{r=d,o=null,a&&(clearTimeout(a),a=null),e(...u)};return[(...u)=>{const d=Date.now(),c=d-r;c>=n?s(u,d):(o=u,a||(a=setTimeout(()=>{a=null,s(o)},n-c)))},()=>o&&s(o)]}const Ir=(e,t,r=3)=>{let n=0;const o=Sl(50,250);return _l(a=>{const s=a.loaded,l=a.lengthComputable?a.total:void 0,i=s-n,u=o(i),d=s<=l;n=s;const c={loaded:s,total:l,progress:l?s/l:void 0,bytes:i,rate:u||void 0,estimated:u&&l&&d?(l-s)/u:void 0,event:a,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(c)},r)},wo=(e,t)=>{const r=e!=null;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},Eo=e=>(...t)=>x.asap(()=>e(...t)),Tl=Ee.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,Ee.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(Ee.origin),Ee.navigator&&/(msie|trident)/i.test(Ee.navigator.userAgent)):()=>!0,Pl=Ee.hasStandardBrowserEnv?{write(e,t,r,n,o,a){const s=[e+"="+encodeURIComponent(t)];x.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),x.isString(n)&&s.push("path="+n),x.isString(o)&&s.push("domain="+o),a===!0&&s.push("secure"),document.cookie=s.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Cl(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Ol(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Ua(e,t,r){let n=!Cl(t);return e&&(n||r==!1)?Ol(e,t):t}const xo=e=>e instanceof Ce?{...e}:e;function gt(e,t){t=t||{};const r={};function n(u,d,c,h){return x.isPlainObject(u)&&x.isPlainObject(d)?x.merge.call({caseless:h},u,d):x.isPlainObject(d)?x.merge({},d):x.isArray(d)?d.slice():d}function o(u,d,c,h){if(x.isUndefined(d)){if(!x.isUndefined(u))return n(void 0,u,c,h)}else return n(u,d,c,h)}function a(u,d){if(!x.isUndefined(d))return n(void 0,d)}function s(u,d){if(x.isUndefined(d)){if(!x.isUndefined(u))return n(void 0,u)}else return n(void 0,d)}function l(u,d,c){if(c in t)return n(u,d);if(c in e)return n(void 0,u)}const i={url:a,method:a,data:a,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:l,headers:(u,d,c)=>o(xo(u),xo(d),c,!0)};return x.forEach(Object.keys({...e,...t}),function(d){const c=i[d]||o,h=c(e[d],t[d],d);x.isUndefined(h)&&c!==l||(r[d]=h)}),r}const ja=e=>{const t=gt({},e);let{data:r,withXSRFToken:n,xsrfHeaderName:o,xsrfCookieName:a,headers:s,auth:l}=t;t.headers=s=Ce.from(s),t.url=Ia(Ua(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&s.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let i;if(x.isFormData(r)){if(Ee.hasStandardBrowserEnv||Ee.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if((i=s.getContentType())!==!1){const[u,...d]=i?i.split(";").map(c=>c.trim()).filter(Boolean):[];s.setContentType([u||"multipart/form-data",...d].join("; "))}}if(Ee.hasStandardBrowserEnv&&(n&&x.isFunction(n)&&(n=n(t)),n||n!==!1&&Tl(t.url))){const u=o&&a&&Pl.read(a);u&&s.set(o,u)}return t},Ll=typeof XMLHttpRequest<"u",Dl=Ll&&function(e){return new Promise(function(r,n){const o=ja(e);let a=o.data;const s=Ce.from(o.headers).normalize();let{responseType:l,onUploadProgress:i,onDownloadProgress:u}=o,d,c,h,b,v;function R(){b&&b(),v&&v(),o.cancelToken&&o.cancelToken.unsubscribe(d),o.signal&&o.signal.removeEventListener("abort",d)}let m=new XMLHttpRequest;m.open(o.method.toUpperCase(),o.url,!0),m.timeout=o.timeout;function w(){if(!m)return;const S=Ce.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders()),D={data:!l||l==="text"||l==="json"?m.responseText:m.response,status:m.status,statusText:m.statusText,headers:S,config:e,request:m};Fa(function(k){r(k),R()},function(k){n(k),R()},D),m=null}"onloadend"in m?m.onloadend=w:m.onreadystatechange=function(){!m||m.readyState!==4||m.status===0&&!(m.responseURL&&m.responseURL.indexOf("file:")===0)||setTimeout(w)},m.onabort=function(){m&&(n(new H("Request aborted",H.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new H("Network Error",H.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let A=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const D=o.transitional||ka;o.timeoutErrorMessage&&(A=o.timeoutErrorMessage),n(new H(A,D.clarifyTimeoutError?H.ETIMEDOUT:H.ECONNABORTED,e,m)),m=null},a===void 0&&s.setContentType(null),"setRequestHeader"in m&&x.forEach(s.toJSON(),function(A,D){m.setRequestHeader(D,A)}),x.isUndefined(o.withCredentials)||(m.withCredentials=!!o.withCredentials),l&&l!=="json"&&(m.responseType=o.responseType),u&&([h,v]=Ir(u,!0),m.addEventListener("progress",h)),i&&m.upload&&([c,b]=Ir(i),m.upload.addEventListener("progress",c),m.upload.addEventListener("loadend",b)),(o.cancelToken||o.signal)&&(d=S=>{m&&(n(!S||S.type?new Pt(null,e,m):S),m.abort(),m=null)},o.cancelToken&&o.cancelToken.subscribe(d),o.signal&&(o.signal.aborted?d():o.signal.addEventListener("abort",d)));const C=Rl(o.url);if(C&&Ee.protocols.indexOf(C)===-1){n(new H("Unsupported protocol "+C+":",H.ERR_BAD_REQUEST,e));return}m.send(a||null)})},Al=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let n=new AbortController,o;const a=function(u){if(!o){o=!0,l();const d=u instanceof Error?u:this.reason;n.abort(d instanceof H?d:new Pt(d instanceof Error?d.message:d))}};let s=t&&setTimeout(()=>{s=null,a(new H(`timeout ${t} of ms exceeded`,H.ETIMEDOUT))},t);const l=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(a):u.removeEventListener("abort",a)}),e=null)};e.forEach(u=>u.addEventListener("abort",a));const{signal:i}=n;return i.unsubscribe=()=>x.asap(l),i}},Il=function*(e,t){let r=e.byteLength;if(r<t){yield e;return}let n=0,o;for(;n<r;)o=n+t,yield e.slice(n,o),n=o},kl=async function*(e,t){for await(const r of Ml(e))yield*Il(r,t)},Ml=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:n}=await t.read();if(r)break;yield n}}finally{await t.cancel()}},Ro=(e,t,r,n)=>{const o=kl(e,t);let a=0,s,l=i=>{s||(s=!0,n&&n(i))};return new ReadableStream({async pull(i){try{const{done:u,value:d}=await o.next();if(u){l(),i.close();return}let c=d.byteLength;if(r){let h=a+=c;r(h)}i.enqueue(new Uint8Array(d))}catch(u){throw l(u),u}},cancel(i){return l(i),o.return()}},{highWaterMark:2})},qr=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Ba=qr&&typeof ReadableStream=="function",Nl=qr&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),$a=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Fl=Ba&&$a(()=>{let e=!1;const t=new Request(Ee.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),So=64*1024,Rn=Ba&&$a(()=>x.isReadableStream(new Response("").body)),kr={stream:Rn&&(e=>e.body)};qr&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!kr[t]&&(kr[t]=x.isFunction(e[t])?r=>r[t]():(r,n)=>{throw new H(`Response type '${t}' is not supported`,H.ERR_NOT_SUPPORT,n)})})})(new Response);const Ul=async e=>{if(e==null)return 0;if(x.isBlob(e))return e.size;if(x.isSpecCompliantForm(e))return(await new Request(Ee.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(x.isArrayBufferView(e)||x.isArrayBuffer(e))return e.byteLength;if(x.isURLSearchParams(e)&&(e=e+""),x.isString(e))return(await Nl(e)).byteLength},jl=async(e,t)=>{const r=x.toFiniteNumber(e.getContentLength());return r??Ul(t)},Bl=qr&&(async e=>{let{url:t,method:r,data:n,signal:o,cancelToken:a,timeout:s,onDownloadProgress:l,onUploadProgress:i,responseType:u,headers:d,withCredentials:c="same-origin",fetchOptions:h}=ja(e);u=u?(u+"").toLowerCase():"text";let b=Al([o,a&&a.toAbortSignal()],s),v;const R=b&&b.unsubscribe&&(()=>{b.unsubscribe()});let m;try{if(i&&Fl&&r!=="get"&&r!=="head"&&(m=await jl(d,n))!==0){let D=new Request(t,{method:"POST",body:n,duplex:"half"}),y;if(x.isFormData(n)&&(y=D.headers.get("content-type"))&&d.setContentType(y),D.body){const[k,j]=wo(m,Ir(Eo(i)));n=Ro(D.body,So,k,j)}}x.isString(c)||(c=c?"include":"omit");const w="credentials"in Request.prototype;v=new Request(t,{...h,signal:b,method:r.toUpperCase(),headers:d.normalize().toJSON(),body:n,duplex:"half",credentials:w?c:void 0});let C=await fetch(v,h);const S=Rn&&(u==="stream"||u==="response");if(Rn&&(l||S&&R)){const D={};["status","statusText","headers"].forEach(B=>{D[B]=C[B]});const y=x.toFiniteNumber(C.headers.get("content-length")),[k,j]=l&&wo(y,Ir(Eo(l),!0))||[];C=new Response(Ro(C.body,So,k,()=>{j&&j(),R&&R()}),D)}u=u||"text";let A=await kr[x.findKey(kr,u)||"text"](C,e);return!S&&R&&R(),await new Promise((D,y)=>{Fa(D,y,{data:A,headers:Ce.from(C.headers),status:C.status,statusText:C.statusText,config:e,request:v})})}catch(w){throw R&&R(),w&&w.name==="TypeError"&&/Load failed|fetch/i.test(w.message)?Object.assign(new H("Network Error",H.ERR_NETWORK,e,v),{cause:w.cause||w}):H.from(w,w&&w.code,e,v)}}),Sn={http:tl,xhr:Dl,fetch:Bl};x.forEach(Sn,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const _o=e=>`- ${e}`,$l=e=>x.isFunction(e)||e===null||e===!1,za={getAdapter:e=>{e=x.isArray(e)?e:[e];const{length:t}=e;let r,n;const o={};for(let a=0;a<t;a++){r=e[a];let s;if(n=r,!$l(r)&&(n=Sn[(s=String(r)).toLowerCase()],n===void 0))throw new H(`Unknown adapter '${s}'`);if(n)break;o[s||"#"+a]=n}if(!n){const a=Object.entries(o).map(([l,i])=>`adapter ${l} `+(i===!1?"is not supported by the environment":"is not available in the build"));let s=t?a.length>1?`since :
`+a.map(_o).join(`
`):" "+_o(a[0]):"as no adapter specified";throw new H("There is no suitable adapter to dispatch the request "+s,"ERR_NOT_SUPPORT")}return n},adapters:Sn};function ln(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Pt(null,e)}function To(e){return ln(e),e.headers=Ce.from(e.headers),e.data=sn.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),za.getAdapter(e.adapter||rr.adapter)(e).then(function(n){return ln(e),n.data=sn.call(e,e.transformResponse,n),n.headers=Ce.from(n.headers),n},function(n){return Na(n)||(ln(e),n&&n.response&&(n.response.data=sn.call(e,e.transformResponse,n.response),n.response.headers=Ce.from(n.response.headers))),Promise.reject(n)})}const Ha="1.11.0",Wr={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Wr[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const Po={};Wr.transitional=function(t,r,n){function o(a,s){return"[Axios v"+Ha+"] Transitional option '"+a+"'"+s+(n?". "+n:"")}return(a,s,l)=>{if(t===!1)throw new H(o(s," has been removed"+(r?" in "+r:"")),H.ERR_DEPRECATED);return r&&!Po[s]&&(Po[s]=!0,console.warn(o(s," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(a,s,l):!0}};Wr.spelling=function(t){return(r,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function zl(e,t,r){if(typeof e!="object")throw new H("options must be an object",H.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let o=n.length;for(;o-- >0;){const a=n[o],s=t[a];if(s){const l=e[a],i=l===void 0||s(l,a,e);if(i!==!0)throw new H("option "+a+" must be "+i,H.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new H("Unknown option "+a,H.ERR_BAD_OPTION)}}const Sr={assertOptions:zl,validators:Wr},qe=Sr.validators;let mt=class{constructor(t){this.defaults=t||{},this.interceptors={request:new vo,response:new vo}}async request(t,r){try{return await this._request(t,r)}catch(n){if(n instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const a=o.stack?o.stack.replace(/^.+\n/,""):"";try{n.stack?a&&!String(n.stack).endsWith(a.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+a):n.stack=a}catch{}}throw n}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=gt(this.defaults,r);const{transitional:n,paramsSerializer:o,headers:a}=r;n!==void 0&&Sr.assertOptions(n,{silentJSONParsing:qe.transitional(qe.boolean),forcedJSONParsing:qe.transitional(qe.boolean),clarifyTimeoutError:qe.transitional(qe.boolean)},!1),o!=null&&(x.isFunction(o)?r.paramsSerializer={serialize:o}:Sr.assertOptions(o,{encode:qe.function,serialize:qe.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),Sr.assertOptions(r,{baseUrl:qe.spelling("baseURL"),withXsrfToken:qe.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let s=a&&x.merge(a.common,a[r.method]);a&&x.forEach(["delete","get","head","post","put","patch","common"],v=>{delete a[v]}),r.headers=Ce.concat(s,a);const l=[];let i=!0;this.interceptors.request.forEach(function(R){typeof R.runWhen=="function"&&R.runWhen(r)===!1||(i=i&&R.synchronous,l.unshift(R.fulfilled,R.rejected))});const u=[];this.interceptors.response.forEach(function(R){u.push(R.fulfilled,R.rejected)});let d,c=0,h;if(!i){const v=[To.bind(this),void 0];for(v.unshift(...l),v.push(...u),h=v.length,d=Promise.resolve(r);c<h;)d=d.then(v[c++],v[c++]);return d}h=l.length;let b=r;for(c=0;c<h;){const v=l[c++],R=l[c++];try{b=v(b)}catch(m){R.call(this,m);break}}try{d=To.call(this,b)}catch(v){return Promise.reject(v)}for(c=0,h=u.length;c<h;)d=d.then(u[c++],u[c++]);return d}getUri(t){t=gt(this.defaults,t);const r=Ua(t.baseURL,t.url,t.allowAbsoluteUrls);return Ia(r,t.params,t.paramsSerializer)}};x.forEach(["delete","get","head","options"],function(t){mt.prototype[t]=function(r,n){return this.request(gt(n||{},{method:t,url:r,data:(n||{}).data}))}});x.forEach(["post","put","patch"],function(t){function r(n){return function(a,s,l){return this.request(gt(l||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:a,data:s}))}}mt.prototype[t]=r(),mt.prototype[t+"Form"]=r(!0)});let Hl=class Va{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(a){r=a});const n=this;this.promise.then(o=>{if(!n._listeners)return;let a=n._listeners.length;for(;a-- >0;)n._listeners[a](o);n._listeners=null}),this.promise.then=o=>{let a;const s=new Promise(l=>{n.subscribe(l),a=l}).then(o);return s.cancel=function(){n.unsubscribe(a)},s},t(function(a,s,l){n.reason||(n.reason=new Pt(a,s,l),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=n=>{t.abort(n)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new Va(function(o){t=o}),cancel:t}}};function Vl(e){return function(r){return e.apply(null,r)}}function ql(e){return x.isObject(e)&&e.isAxiosError===!0}const _n={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(_n).forEach(([e,t])=>{_n[t]=e});function qa(e){const t=new mt(e),r=Ea(mt.prototype.request,t);return x.extend(r,mt.prototype,t,{allOwnKeys:!0}),x.extend(r,t,null,{allOwnKeys:!0}),r.create=function(o){return qa(gt(e,o))},r}const pe=qa(rr);pe.Axios=mt;pe.CanceledError=Pt;pe.CancelToken=Hl;pe.isCancel=Na;pe.VERSION=Ha;pe.toFormData=Vr;pe.AxiosError=H;pe.Cancel=pe.CanceledError;pe.all=function(t){return Promise.all(t)};pe.spread=Vl;pe.isAxiosError=ql;pe.mergeConfig=gt;pe.AxiosHeaders=Ce;pe.formToJSON=e=>Ma(x.isHTMLForm(e)?new FormData(e):e);pe.getAdapter=za.getAdapter;pe.HttpStatusCode=_n;pe.default=pe;const{Axios:Ah,AxiosError:Ih,CanceledError:kh,isCancel:Mh,CancelToken:Nh,VERSION:Fh,all:Uh,Cancel:jh,isAxiosError:Bh,spread:$h,toFormData:zh,AxiosHeaders:Hh,HttpStatusCode:Vh,formToJSON:qh,getAdapter:Wh,mergeConfig:Kh}=pe;/**
 * react-router v7.7.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var Wa=e=>{throw TypeError(e)},Wl=(e,t,r)=>t.has(e)||Wa("Cannot "+r),un=(e,t,r)=>(Wl(e,t,"read from private field"),r?r.call(e):t.get(e)),Kl=(e,t,r)=>t.has(e)?Wa("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),Co="popstate";function Jl(e={}){function t(o,a){let{pathname:s="/",search:l="",hash:i=""}=Ye(o.location.hash.substring(1));return!s.startsWith("/")&&!s.startsWith(".")&&(s="/"+s),Qt("",{pathname:s,search:l,hash:i},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(o,a){let s=o.document.querySelector("base"),l="";if(s&&s.getAttribute("href")){let i=o.location.href,u=i.indexOf("#");l=u===-1?i:i.slice(0,u)}return l+"#"+(typeof a=="string"?a:st(a))}function n(o,a){de(o.pathname.charAt(0)==="/",`relative pathnames are not supported in hash history.push(${JSON.stringify(a)})`)}return Yl(t,r,n,e)}function W(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function de(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Ql(){return Math.random().toString(36).substring(2,10)}function Oo(e,t){return{usr:e.state,key:e.key,idx:t}}function Qt(e,t,r=null,n){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?Ye(t):t,state:r,key:t&&t.key||n||Ql()}}function st({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function Ye(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substring(n),e=e.substring(0,n)),e&&(t.pathname=e)}return t}function Yl(e,t,r,n={}){let{window:o=document.defaultView,v5Compat:a=!1}=n,s=o.history,l="POP",i=null,u=d();u==null&&(u=0,s.replaceState({...s.state,idx:u},""));function d(){return(s.state||{idx:null}).idx}function c(){l="POP";let m=d(),w=m==null?null:m-u;u=m,i&&i({action:l,location:R.location,delta:w})}function h(m,w){l="PUSH";let C=Qt(R.location,m,w);r&&r(C,m),u=d()+1;let S=Oo(C,u),A=R.createHref(C);try{s.pushState(S,"",A)}catch(D){if(D instanceof DOMException&&D.name==="DataCloneError")throw D;o.location.assign(A)}a&&i&&i({action:l,location:R.location,delta:1})}function b(m,w){l="REPLACE";let C=Qt(R.location,m,w);r&&r(C,m),u=d();let S=Oo(C,u),A=R.createHref(C);s.replaceState(S,"",A),a&&i&&i({action:l,location:R.location,delta:0})}function v(m){return Ka(m)}let R={get action(){return l},get location(){return e(o,s)},listen(m){if(i)throw new Error("A history only accepts one active listener");return o.addEventListener(Co,c),i=m,()=>{o.removeEventListener(Co,c),i=null}},createHref(m){return t(o,m)},createURL:v,encodeLocation(m){let w=v(m);return{pathname:w.pathname,search:w.search,hash:w.hash}},push:h,replace:b,go(m){return s.go(m)}};return R}function Ka(e,t=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),W(r,"No window.location.(origin|href) available to create URL");let n=typeof e=="string"?e:st(e);return n=n.replace(/ $/,"%20"),!t&&n.startsWith("//")&&(n=r+n),new URL(n,r)}var qt,Lo=class{constructor(e){if(Kl(this,qt,new Map),e)for(let[t,r]of e)this.set(t,r)}get(e){if(un(this,qt).has(e))return un(this,qt).get(e);if(e.defaultValue!==void 0)return e.defaultValue;throw new Error("No value found for context")}set(e,t){un(this,qt).set(e,t)}};qt=new WeakMap;var Xl=new Set(["lazy","caseSensitive","path","id","index","children"]);function Gl(e){return Xl.has(e)}var Zl=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function eu(e){return Zl.has(e)}function tu(e){return e.index===!0}function Yt(e,t,r=[],n={},o=!1){return e.map((a,s)=>{let l=[...r,String(s)],i=typeof a.id=="string"?a.id:l.join("-");if(W(a.index!==!0||!a.children,"Cannot specify children on an index route"),W(o||!n[i],`Found a route id collision on id "${i}".  Route id's must be globally unique within Data Router usages`),tu(a)){let u={...a,...t(a),id:i};return n[i]=u,u}else{let u={...a,...t(a),id:i,children:void 0};return n[i]=u,a.children&&(u.children=Yt(a.children,t,l,n,o)),u}})}function ot(e,t,r="/"){return _r(e,t,r,!1)}function _r(e,t,r,n){let o=typeof t=="string"?Ye(t):t,a=Me(o.pathname||"/",r);if(a==null)return null;let s=Ja(e);nu(s);let l=null;for(let i=0;l==null&&i<s.length;++i){let u=pu(a);l=fu(s[i],u,n)}return l}function ru(e,t){let{route:r,pathname:n,params:o}=e;return{id:r.id,pathname:n,params:o,data:t[r.id],handle:r.handle}}function Ja(e,t=[],r=[],n=""){let o=(a,s,l)=>{let i={relativePath:l===void 0?a.path||"":l,caseSensitive:a.caseSensitive===!0,childrenIndex:s,route:a};i.relativePath.startsWith("/")&&(W(i.relativePath.startsWith(n),`Absolute route path "${i.relativePath}" nested under path "${n}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),i.relativePath=i.relativePath.slice(n.length));let u=Ke([n,i.relativePath]),d=r.concat(i);a.children&&a.children.length>0&&(W(a.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${u}".`),Ja(a.children,t,d,u)),!(a.path==null&&!a.index)&&t.push({path:u,score:cu(u,a.index),routesMeta:d})};return e.forEach((a,s)=>{if(a.path===""||!a.path?.includes("?"))o(a,s);else for(let l of Qa(a.path))o(a,s,l)}),t}function Qa(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,o=r.endsWith("?"),a=r.replace(/\?$/,"");if(n.length===0)return o?[a,""]:[a];let s=Qa(n.join("/")),l=[];return l.push(...s.map(i=>i===""?a:[a,i].join("/"))),o&&l.push(...s),l.map(i=>e.startsWith("/")&&i===""?"/":i)}function nu(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:du(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}var ou=/^:[\w-]+$/,au=3,su=2,iu=1,lu=10,uu=-2,Do=e=>e==="*";function cu(e,t){let r=e.split("/"),n=r.length;return r.some(Do)&&(n+=uu),t&&(n+=su),r.filter(o=>!Do(o)).reduce((o,a)=>o+(ou.test(a)?au:a===""?iu:lu),n)}function du(e,t){return e.length===t.length&&e.slice(0,-1).every((n,o)=>n===t[o])?e[e.length-1]-t[t.length-1]:0}function fu(e,t,r=!1){let{routesMeta:n}=e,o={},a="/",s=[];for(let l=0;l<n.length;++l){let i=n[l],u=l===n.length-1,d=a==="/"?t:t.slice(a.length)||"/",c=Mr({path:i.relativePath,caseSensitive:i.caseSensitive,end:u},d),h=i.route;if(!c&&u&&r&&!n[n.length-1].route.index&&(c=Mr({path:i.relativePath,caseSensitive:i.caseSensitive,end:!1},d)),!c)return null;Object.assign(o,c.params),s.push({params:o,pathname:Ke([a,c.pathname]),pathnameBase:vu(Ke([a,c.pathnameBase])),route:h}),c.pathnameBase!=="/"&&(a=Ke([a,c.pathnameBase]))}return s}function Mr(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=hu(e.path,e.caseSensitive,e.end),o=t.match(r);if(!o)return null;let a=o[0],s=a.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:n.reduce((u,{paramName:d,isOptional:c},h)=>{if(d==="*"){let v=l[h]||"";s=a.slice(0,a.length-v.length).replace(/(.)\/+$/,"$1")}const b=l[h];return c&&!b?u[d]=void 0:u[d]=(b||"").replace(/%2F/g,"/"),u},{}),pathname:a,pathnameBase:s,pattern:e}}function hu(e,t=!1,r=!0){de(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let n=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,l,i)=>(n.push({paramName:l,isOptional:i!=null}),i?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),n]}function pu(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return de(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function Me(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function mu({basename:e,pathname:t}){return t==="/"?e:Ke([e,t])}function gu(e,t="/"){let{pathname:r,search:n="",hash:o=""}=typeof e=="string"?Ye(e):e;return{pathname:r?r.startsWith("/")?r:yu(r,t):t,search:bu(n),hash:wu(o)}}function yu(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?r.length>1&&r.pop():o!=="."&&r.push(o)}),r.length>1?r.join("/"):"/"}function cn(e,t,r,n){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(n)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Ya(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function Kr(e){let t=Ya(e);return t.map((r,n)=>n===t.length-1?r.pathname:r.pathnameBase)}function Jr(e,t,r,n=!1){let o;typeof e=="string"?o=Ye(e):(o={...e},W(!o.pathname||!o.pathname.includes("?"),cn("?","pathname","search",o)),W(!o.pathname||!o.pathname.includes("#"),cn("#","pathname","hash",o)),W(!o.search||!o.search.includes("#"),cn("#","search","hash",o)));let a=e===""||o.pathname==="",s=a?"/":o.pathname,l;if(s==null)l=r;else{let c=t.length-1;if(!n&&s.startsWith("..")){let h=s.split("/");for(;h[0]==="..";)h.shift(),c-=1;o.pathname=h.join("/")}l=c>=0?t[c]:"/"}let i=gu(o,l),u=s&&s!=="/"&&s.endsWith("/"),d=(a||s===".")&&r.endsWith("/");return!i.pathname.endsWith("/")&&(u||d)&&(i.pathname+="/"),i}var Ke=e=>e.join("/").replace(/\/\/+/g,"/"),vu=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),bu=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,wu=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e,Nr=class{constructor(e,t,r,n=!1){this.status=e,this.statusText=t||"",this.internal=n,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function Xt(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var Xa=["POST","PUT","PATCH","DELETE"],Eu=new Set(Xa),xu=["GET",...Xa],Ru=new Set(xu),Su=new Set([301,302,303,307,308]),_u=new Set([307,308]),dn={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Tu={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Bt={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},Pu=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,zn=e=>Pu.test(e),Cu=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),Ga="remix-router-transitions",Za=Symbol("ResetLoaderData");function Ou(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u";W(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let n=e.hydrationRouteProperties||[],o=e.mapRouteProperties||Cu,a={},s=Yt(e.routes,o,void 0,a),l,i=e.basename||"/",u=e.dataStrategy||ku,d={unstable_middleware:!1,...e.future},c=null,h=new Set,b=null,v=null,R=null,m=e.hydrationData!=null,w=ot(s,e.history.location,i),C=!1,S=null,A;if(w==null&&!e.patchRoutesOnNavigation){let f=ke(404,{pathname:e.history.location.pathname}),{matches:p,route:E}=Ho(s);A=!0,w=p,S={[E.id]:f}}else if(w&&!e.hydrationData&&z(w,s,e.history.location.pathname).active&&(w=null),w)if(w.some(f=>f.route.lazy))A=!1;else if(!w.some(f=>f.route.loader))A=!0;else{let f=e.hydrationData?e.hydrationData.loaderData:null,p=e.hydrationData?e.hydrationData.errors:null;if(p){let E=w.findIndex(_=>p[_.route.id]!==void 0);A=w.slice(0,E+1).every(_=>!Pn(_.route,f,p))}else A=w.every(E=>!Pn(E.route,f,p))}else{A=!1,w=[];let f=z(null,s,e.history.location.pathname);f.active&&f.matches&&(C=!0,w=f.matches)}let D,y={historyAction:e.history.action,location:e.history.location,matches:w,initialized:A,navigation:dn,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||S,fetchers:new Map,blockers:new Map},k="POP",j=!1,B,K=!1,te=new Map,Se=null,X=!1,ee=!1,ae=new Set,I=new Map,ne=0,oe=-1,se=new Map,me=new Set,q=new Map,Q=new Map,J=new Set,ge=new Map,De,G=null;function it(){if(c=e.history.listen(({action:f,location:p,delta:E})=>{if(De){De(),De=void 0;return}de(ge.size===0||E!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let _=bt({currentLocation:y.location,nextLocation:p,historyAction:f});if(_&&E!=null){let T=new Promise(O=>{De=O});e.history.go(E*-1),vt(_,{state:"blocked",location:p,proceed(){vt(_,{state:"proceeding",proceed:void 0,reset:void 0,location:p}),T.then(()=>e.history.go(E))},reset(){let O=new Map(y.blockers);O.set(_,Bt),le({blockers:O})}});return}return Ne(f,p)}),r){qu(t,te);let f=()=>Wu(t,te);t.addEventListener("pagehide",f),Se=()=>t.removeEventListener("pagehide",f)}return y.initialized||Ne("POP",y.location,{initialHydration:!0}),D}function Ge(){c&&c(),Se&&Se(),h.clear(),B&&B.abort(),y.fetchers.forEach((f,p)=>ut(p)),y.blockers.forEach((f,p)=>kt(p))}function lt(f){return h.add(f),()=>h.delete(f)}function le(f,p={}){f.matches&&(f.matches=f.matches.map(T=>{let O=a[T.route.id],M=T.route;return M.element!==O.element||M.errorElement!==O.errorElement||M.hydrateFallbackElement!==O.hydrateFallbackElement?{...T,route:O}:T})),y={...y,...f};let E=[],_=[];y.fetchers.forEach((T,O)=>{T.state==="idle"&&(J.has(O)?E.push(O):_.push(O))}),J.forEach(T=>{!y.fetchers.has(T)&&!I.has(T)&&E.push(T)}),[...h].forEach(T=>T(y,{deletedFetchers:E,viewTransitionOpts:p.viewTransitionOpts,flushSync:p.flushSync===!0})),E.forEach(T=>ut(T)),_.forEach(T=>y.fetchers.delete(T))}function Ze(f,p,{flushSync:E}={}){let _=y.actionData!=null&&y.navigation.formMethod!=null&&Te(y.navigation.formMethod)&&y.navigation.state==="loading"&&f.state?._isRedirect!==!0,T;p.actionData?Object.keys(p.actionData).length>0?T=p.actionData:T=null:_?T=y.actionData:T=null;let O=p.loaderData?$o(y.loaderData,p.loaderData,p.matches||[],p.errors):y.loaderData,M=y.blockers;M.size>0&&(M=new Map(M),M.forEach((F,U)=>M.set(U,Bt)));let L=X?!1:Mt(f,p.matches||y.matches),N=j===!0||y.navigation.formMethod!=null&&Te(y.navigation.formMethod)&&f.state?._isRedirect!==!0;l&&(s=l,l=void 0),X||k==="POP"||(k==="PUSH"?e.history.push(f,f.state):k==="REPLACE"&&e.history.replace(f,f.state));let $;if(k==="POP"){let F=te.get(y.location.pathname);F&&F.has(f.pathname)?$={currentLocation:y.location,nextLocation:f}:te.has(f.pathname)&&($={currentLocation:f,nextLocation:y.location})}else if(K){let F=te.get(y.location.pathname);F?F.add(f.pathname):(F=new Set([f.pathname]),te.set(y.location.pathname,F)),$={currentLocation:y.location,nextLocation:f}}le({...p,actionData:T,loaderData:O,historyAction:k,location:f,initialized:!0,navigation:dn,revalidation:"idle",restoreScrollPosition:L,preventScrollReset:N,blockers:M},{viewTransitionOpts:$,flushSync:E===!0}),k="POP",j=!1,K=!1,X=!1,ee=!1,G?.resolve(),G=null}async function Dt(f,p){if(typeof f=="number"){e.history.go(f);return}let E=Tn(y.location,y.matches,i,f,p?.fromRouteId,p?.relative),{path:_,submission:T,error:O}=Ao(!1,E,p),M=y.location,L=Qt(y.location,_,p&&p.state);L={...L,...e.history.encodeLocation(L)};let N=p&&p.replace!=null?p.replace:void 0,$="PUSH";N===!0?$="REPLACE":N===!1||T!=null&&Te(T.formMethod)&&T.formAction===y.location.pathname+y.location.search&&($="REPLACE");let F=p&&"preventScrollReset"in p?p.preventScrollReset===!0:void 0,U=(p&&p.flushSync)===!0,Y=bt({currentLocation:M,nextLocation:L,historyAction:$});if(Y){vt(Y,{state:"blocked",location:L,proceed(){vt(Y,{state:"proceeding",proceed:void 0,reset:void 0,location:L}),Dt(f,p)},reset(){let ue=new Map(y.blockers);ue.set(Y,Bt),le({blockers:ue})}});return}await Ne($,L,{submission:T,pendingError:O,preventScrollReset:F,replace:p&&p.replace,enableViewTransition:p&&p.viewTransition,flushSync:U})}function Zr(){G||(G=Ku()),At(),le({revalidation:"loading"});let f=G.promise;return y.navigation.state==="submitting"?f:y.navigation.state==="idle"?(Ne(y.historyAction,y.location,{startUninterruptedRevalidation:!0}),f):(Ne(k||y.historyAction,y.navigation.location,{overrideNavigation:y.navigation,enableViewTransition:K===!0}),f)}async function Ne(f,p,E){B&&B.abort(),B=null,k=f,X=(E&&E.startUninterruptedRevalidation)===!0,hr(y.location,y.matches),j=(E&&E.preventScrollReset)===!0,K=(E&&E.enableViewTransition)===!0;let _=l||s,T=E&&E.overrideNavigation,O=E?.initialHydration&&y.matches&&y.matches.length>0&&!C?y.matches:ot(_,p,i),M=(E&&E.flushSync)===!0;if(O&&y.initialized&&!ee&&Bu(y.location,p)&&!(E&&E.submission&&Te(E.submission.formMethod))){Ze(p,{matches:O},{flushSync:M});return}let L=z(O,_,p.pathname);if(L.active&&L.matches&&(O=L.matches),!O){let{error:Re,notFoundMatches:ce,route:re}=Ie(p.pathname);Ze(p,{matches:ce,loaderData:{},errors:{[re.id]:Re}},{flushSync:M});return}B=new AbortController;let N=Et(e.history,p,B.signal,E&&E.submission),$=new Lo(e.unstable_getContext?await e.unstable_getContext():void 0),F;if(E&&E.pendingError)F=[ft(O).route.id,{type:"error",error:E.pendingError}];else if(E&&E.submission&&Te(E.submission.formMethod)){let Re=await en(N,p,E.submission,O,$,L.active,E&&E.initialHydration===!0,{replace:E.replace,flushSync:M});if(Re.shortCircuited)return;if(Re.pendingActionResult){let[ce,re]=Re.pendingActionResult;if(Le(re)&&Xt(re.error)&&re.error.status===404){B=null,Ze(p,{matches:Re.matches,loaderData:{},errors:{[ce]:re.error}});return}}O=Re.matches||O,F=Re.pendingActionResult,T=fn(p,E.submission),M=!1,L.active=!1,N=Et(e.history,N.url,N.signal)}let{shortCircuited:U,matches:Y,loaderData:ue,errors:be}=await sr(N,p,O,$,L.active,T,E&&E.submission,E&&E.fetcherSubmission,E&&E.replace,E&&E.initialHydration===!0,M,F);U||(B=null,Ze(p,{matches:Y||O,...zo(F),loaderData:ue,errors:be}))}async function en(f,p,E,_,T,O,M,L={}){At();let N=Hu(p,E);if(le({navigation:N},{flushSync:L.flushSync===!0}),O){let U=await ie(_,p.pathname,f.signal);if(U.type==="aborted")return{shortCircuited:!0};if(U.type==="error"){let Y=ft(U.partialMatches).route.id;return{matches:U.partialMatches,pendingActionResult:[Y,{type:"error",error:U.error}]}}else if(U.matches)_=U.matches;else{let{notFoundMatches:Y,error:ue,route:be}=Ie(p.pathname);return{matches:Y,pendingActionResult:[be.id,{type:"error",error:ue}]}}}let $,F=Tr(_,p);if(!F.route.action&&!F.route.lazy)$={type:"error",error:ke(405,{method:f.method,pathname:p.pathname,routeId:F.route.id})};else{let U=Rt(o,a,f,_,F,M?[]:n,T),Y=await Ae(f,U,T,null);if($=Y[F.route.id],!$){for(let ue of _)if(Y[ue.route.id]){$=Y[ue.route.id];break}}if(f.signal.aborted)return{shortCircuited:!0}}if(pt($)){let U;return L&&L.replace!=null?U=L.replace:U=Uo($.response.headers.get("Location"),new URL(f.url),i)===y.location.pathname+y.location.search,await fe(f,$,!0,{submission:E,replace:U}),{shortCircuited:!0}}if(Le($)){let U=ft(_,F.route.id);return(L&&L.replace)!==!0&&(k="PUSH"),{matches:_,pendingActionResult:[U.route.id,$,F.route.id]}}return{matches:_,pendingActionResult:[F.route.id,$]}}async function sr(f,p,E,_,T,O,M,L,N,$,F,U){let Y=O||fn(p,M),ue=M||L||qo(Y),be=!X&&!$;if(T){if(be){let we=et(U);le({navigation:Y,...we!==void 0?{actionData:we}:{}},{flushSync:F})}let Z=await ie(E,p.pathname,f.signal);if(Z.type==="aborted")return{shortCircuited:!0};if(Z.type==="error"){let we=ft(Z.partialMatches).route.id;return{matches:Z.partialMatches,loaderData:{},errors:{[we]:Z.error}}}else if(Z.matches)E=Z.matches;else{let{error:we,notFoundMatches:gr,route:Ut}=Ie(p.pathname);return{matches:gr,loaderData:{},errors:{[Ut.id]:we}}}}let Re=l||s,{dsMatches:ce,revalidatingFetchers:re}=Io(f,_,o,a,e.history,y,E,ue,p,$?[]:n,$===!0,ee,ae,J,q,me,Re,i,e.patchRoutesOnNavigation!=null,U);if(oe=++ne,!e.dataStrategy&&!ce.some(Z=>Z.shouldLoad)&&re.length===0){let Z=ur();return Ze(p,{matches:E,loaderData:{},errors:U&&Le(U[1])?{[U[0]]:U[1].error}:null,...zo(U),...Z?{fetchers:new Map(y.fetchers)}:{}},{flushSync:F}),{shortCircuited:!0}}if(be){let Z={};if(!T){Z.navigation=Y;let we=et(U);we!==void 0&&(Z.actionData=we)}re.length>0&&(Z.fetchers=ir(re)),le(Z,{flushSync:F})}re.forEach(Z=>{je(Z.key),Z.controller&&I.set(Z.key,Z.controller)});let ct=()=>re.forEach(Z=>je(Z.key));B&&B.signal.addEventListener("abort",ct);let{loaderResults:Nt,fetcherResults:rt}=await lr(ce,re,f,_);if(f.signal.aborted)return{shortCircuited:!0};B&&B.signal.removeEventListener("abort",ct),re.forEach(Z=>I.delete(Z.key));let Ve=vr(Nt);if(Ve)return await fe(f,Ve.result,!0,{replace:N}),{shortCircuited:!0};if(Ve=vr(rt),Ve)return me.add(Ve.key),await fe(f,Ve.result,!0,{replace:N}),{shortCircuited:!0};let{loaderData:on,errors:Ft}=Bo(y,E,Nt,U,re,rt);$&&y.errors&&(Ft={...y.errors,...Ft});let dt=ur(),pr=cr(oe),mr=dt||pr||re.length>0;return{matches:E,loaderData:on,errors:Ft,...mr?{fetchers:new Map(y.fetchers)}:{}}}function et(f){if(f&&!Le(f[1]))return{[f[0]]:f[1].data};if(y.actionData)return Object.keys(y.actionData).length===0?null:y.actionData}function ir(f){return f.forEach(p=>{let E=y.fetchers.get(p.key),_=$t(void 0,E?E.data:void 0);y.fetchers.set(p.key,_)}),new Map(y.fetchers)}async function Fe(f,p,E,_){je(f);let T=(_&&_.flushSync)===!0,O=l||s,M=Tn(y.location,y.matches,i,E,p,_?.relative),L=ot(O,M,i),N=z(L,O,M);if(N.active&&N.matches&&(L=N.matches),!L){Ue(f,p,ke(404,{pathname:M}),{flushSync:T});return}let{path:$,submission:F,error:U}=Ao(!0,M,_);if(U){Ue(f,p,U,{flushSync:T});return}let Y=new Lo(e.unstable_getContext?await e.unstable_getContext():void 0),ue=(_&&_.preventScrollReset)===!0;if(F&&Te(F.formMethod)){await tn(f,p,$,L,Y,N.active,T,ue,F);return}q.set(f,{routeId:p,path:$}),await rn(f,p,$,L,Y,N.active,T,ue,F)}async function tn(f,p,E,_,T,O,M,L,N){At(),q.delete(f);let $=y.fetchers.get(f);Oe(f,Vu(N,$),{flushSync:M});let F=new AbortController,U=Et(e.history,E,F.signal,N);if(O){let ye=await ie(_,new URL(U.url).pathname,U.signal,f);if(ye.type==="aborted")return;if(ye.type==="error"){Ue(f,p,ye.error,{flushSync:M});return}else if(ye.matches)_=ye.matches;else{Ue(f,p,ke(404,{pathname:E}),{flushSync:M});return}}let Y=Tr(_,E);if(!Y.route.action&&!Y.route.lazy){let ye=ke(405,{method:N.formMethod,pathname:E,routeId:p});Ue(f,p,ye,{flushSync:M});return}I.set(f,F);let ue=ne,be=Rt(o,a,U,_,Y,n,T),ce=(await Ae(U,be,T,f))[Y.route.id];if(U.signal.aborted){I.get(f)===F&&I.delete(f);return}if(J.has(f)){if(pt(ce)||Le(ce)){Oe(f,nt(void 0));return}}else{if(pt(ce))if(I.delete(f),oe>ue){Oe(f,nt(void 0));return}else return me.add(f),Oe(f,$t(N)),fe(U,ce,!1,{fetcherSubmission:N,preventScrollReset:L});if(Le(ce)){Ue(f,p,ce.error);return}}let re=y.navigation.location||y.location,ct=Et(e.history,re,F.signal),Nt=l||s,rt=y.navigation.state!=="idle"?ot(Nt,y.navigation.location,i):y.matches;W(rt,"Didn't find any matches after fetcher action");let Ve=++ne;se.set(f,Ve);let on=$t(N,ce.data);y.fetchers.set(f,on);let{dsMatches:Ft,revalidatingFetchers:dt}=Io(ct,T,o,a,e.history,y,rt,N,re,n,!1,ee,ae,J,q,me,Nt,i,e.patchRoutesOnNavigation!=null,[Y.route.id,ce]);dt.filter(ye=>ye.key!==f).forEach(ye=>{let yr=ye.key,so=y.fetchers.get(yr),Ks=$t(void 0,so?so.data:void 0);y.fetchers.set(yr,Ks),je(yr),ye.controller&&I.set(yr,ye.controller)}),le({fetchers:new Map(y.fetchers)});let pr=()=>dt.forEach(ye=>je(ye.key));F.signal.addEventListener("abort",pr);let{loaderResults:mr,fetcherResults:Z}=await lr(Ft,dt,ct,T);if(F.signal.aborted)return;if(F.signal.removeEventListener("abort",pr),se.delete(f),I.delete(f),dt.forEach(ye=>I.delete(ye.key)),y.fetchers.has(f)){let ye=nt(ce.data);y.fetchers.set(f,ye)}let we=vr(mr);if(we)return fe(ct,we.result,!1,{preventScrollReset:L});if(we=vr(Z),we)return me.add(we.key),fe(ct,we.result,!1,{preventScrollReset:L});let{loaderData:gr,errors:Ut}=Bo(y,rt,mr,void 0,dt,Z);cr(Ve),y.navigation.state==="loading"&&Ve>oe?(W(k,"Expected pending action"),B&&B.abort(),Ze(y.navigation.location,{matches:rt,loaderData:gr,errors:Ut,fetchers:new Map(y.fetchers)})):(le({errors:Ut,loaderData:$o(y.loaderData,gr,rt,Ut),fetchers:new Map(y.fetchers)}),ee=!1)}async function rn(f,p,E,_,T,O,M,L,N){let $=y.fetchers.get(f);Oe(f,$t(N,$?$.data:void 0),{flushSync:M});let F=new AbortController,U=Et(e.history,E,F.signal);if(O){let re=await ie(_,new URL(U.url).pathname,U.signal,f);if(re.type==="aborted")return;if(re.type==="error"){Ue(f,p,re.error,{flushSync:M});return}else if(re.matches)_=re.matches;else{Ue(f,p,ke(404,{pathname:E}),{flushSync:M});return}}let Y=Tr(_,E);I.set(f,F);let ue=ne,be=Rt(o,a,U,_,Y,n,T),ce=(await Ae(U,be,T,f))[Y.route.id];if(I.get(f)===F&&I.delete(f),!U.signal.aborted){if(J.has(f)){Oe(f,nt(void 0));return}if(pt(ce))if(oe>ue){Oe(f,nt(void 0));return}else{me.add(f),await fe(U,ce,!1,{preventScrollReset:L});return}if(Le(ce)){Ue(f,p,ce.error);return}Oe(f,nt(ce.data))}}async function fe(f,p,E,{submission:_,fetcherSubmission:T,preventScrollReset:O,replace:M}={}){p.response.headers.has("X-Remix-Revalidate")&&(ee=!0);let L=p.response.headers.get("Location");W(L,"Expected a Location header on the redirect Response"),L=Uo(L,new URL(f.url),i);let N=Qt(y.location,L,{_isRedirect:!0});if(r){let be=!1;if(p.response.headers.has("X-Remix-Reload-Document"))be=!0;else if(zn(L)){const Re=Ka(L,!0);be=Re.origin!==t.location.origin||Me(Re.pathname,i)==null}if(be){M?t.location.replace(L):t.location.assign(L);return}}B=null;let $=M===!0||p.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:F,formAction:U,formEncType:Y}=y.navigation;!_&&!T&&F&&U&&Y&&(_=qo(y.navigation));let ue=_||T;if(_u.has(p.response.status)&&ue&&Te(ue.formMethod))await Ne($,N,{submission:{...ue,formAction:L},preventScrollReset:O||j,enableViewTransition:E?K:void 0});else{let be=fn(N,_);await Ne($,N,{overrideNavigation:be,fetcherSubmission:T,preventScrollReset:O||j,enableViewTransition:E?K:void 0})}}async function Ae(f,p,E,_){let T,O={};try{T=await Mu(u,f,p,_,E,!1)}catch(M){return p.filter(L=>L.shouldLoad).forEach(L=>{O[L.route.id]={type:"error",error:M}}),O}if(f.signal.aborted)return O;for(let[M,L]of Object.entries(T))if($u(L)){let N=L.result;O[M]={type:"redirect",response:Uu(N,f,M,p,i)}}else O[M]=await Fu(L);return O}async function lr(f,p,E,_){let T=Ae(E,f,_,null),O=Promise.all(p.map(async N=>{if(N.matches&&N.match&&N.request&&N.controller){let F=(await Ae(N.request,N.matches,_,N.key))[N.match.route.id];return{[N.key]:F}}else return Promise.resolve({[N.key]:{type:"error",error:ke(404,{pathname:N.path})}})})),M=await T,L=(await O).reduce((N,$)=>Object.assign(N,$),{});return{loaderResults:M,fetcherResults:L}}function At(){ee=!0,q.forEach((f,p)=>{I.has(p)&&ae.add(p),je(p)})}function Oe(f,p,E={}){y.fetchers.set(f,p),le({fetchers:new Map(y.fetchers)},{flushSync:(E&&E.flushSync)===!0})}function Ue(f,p,E,_={}){let T=ft(y.matches,p);ut(f),le({errors:{[T.route.id]:E},fetchers:new Map(y.fetchers)},{flushSync:(_&&_.flushSync)===!0})}function It(f){return Q.set(f,(Q.get(f)||0)+1),J.has(f)&&J.delete(f),y.fetchers.get(f)||Tu}function ut(f){let p=y.fetchers.get(f);I.has(f)&&!(p&&p.state==="loading"&&se.has(f))&&je(f),q.delete(f),se.delete(f),me.delete(f),J.delete(f),ae.delete(f),y.fetchers.delete(f)}function tt(f){let p=(Q.get(f)||0)-1;p<=0?(Q.delete(f),J.add(f)):Q.set(f,p),le({fetchers:new Map(y.fetchers)})}function je(f){let p=I.get(f);p&&(p.abort(),I.delete(f))}function Qe(f){for(let p of f){let E=It(p),_=nt(E.data);y.fetchers.set(p,_)}}function ur(){let f=[],p=!1;for(let E of me){let _=y.fetchers.get(E);W(_,`Expected fetcher: ${E}`),_.state==="loading"&&(me.delete(E),f.push(E),p=!0)}return Qe(f),p}function cr(f){let p=[];for(let[E,_]of se)if(_<f){let T=y.fetchers.get(E);W(T,`Expected fetcher: ${E}`),T.state==="loading"&&(je(E),se.delete(E),p.push(E))}return Qe(p),p.length>0}function dr(f,p){let E=y.blockers.get(f)||Bt;return ge.get(f)!==p&&ge.set(f,p),E}function kt(f){y.blockers.delete(f),ge.delete(f)}function vt(f,p){let E=y.blockers.get(f)||Bt;W(E.state==="unblocked"&&p.state==="blocked"||E.state==="blocked"&&p.state==="blocked"||E.state==="blocked"&&p.state==="proceeding"||E.state==="blocked"&&p.state==="unblocked"||E.state==="proceeding"&&p.state==="unblocked",`Invalid blocker state transition: ${E.state} -> ${p.state}`);let _=new Map(y.blockers);_.set(f,p),le({blockers:_})}function bt({currentLocation:f,nextLocation:p,historyAction:E}){if(ge.size===0)return;ge.size>1&&de(!1,"A router only supports one blocker at a time");let _=Array.from(ge.entries()),[T,O]=_[_.length-1],M=y.blockers.get(T);if(!(M&&M.state==="proceeding")&&O({currentLocation:f,nextLocation:p,historyAction:E}))return T}function Ie(f){let p=ke(404,{pathname:f}),E=l||s,{matches:_,route:T}=Ho(E);return{notFoundMatches:_,route:T,error:p}}function nn(f,p,E){if(b=f,R=p,v=E||null,!m&&y.navigation===dn){m=!0;let _=Mt(y.location,y.matches);_!=null&&le({restoreScrollPosition:_})}return()=>{b=null,R=null,v=null}}function fr(f,p){return v&&v(f,p.map(_=>ru(_,y.loaderData)))||f.key}function hr(f,p){if(b&&R){let E=fr(f,p);b[E]=R()}}function Mt(f,p){if(b){let E=fr(f,p),_=b[E];if(typeof _=="number")return _}return null}function z(f,p,E){if(e.patchRoutesOnNavigation)if(f){if(Object.keys(f[0].params).length>0)return{active:!0,matches:_r(p,E,i,!0)}}else return{active:!0,matches:_r(p,E,i,!0)||[]};return{active:!1,matches:null}}async function ie(f,p,E,_){if(!e.patchRoutesOnNavigation)return{type:"success",matches:f};let T=f;for(;;){let O=l==null,M=l||s,L=a;try{await e.patchRoutesOnNavigation({signal:E,path:p,matches:T,fetcherKey:_,patch:(F,U)=>{E.aborted||ko(F,U,M,L,o,!1)}})}catch(F){return{type:"error",error:F,partialMatches:T}}finally{O&&!E.aborted&&(s=[...s])}if(E.aborted)return{type:"aborted"};let N=ot(M,p,i);if(N)return{type:"success",matches:N};let $=_r(M,p,i,!0);if(!$||T.length===$.length&&T.every((F,U)=>F.route.id===$[U].route.id))return{type:"success",matches:null};T=$}}function ve(f){a={},l=Yt(f,o,void 0,a)}function xe(f,p,E=!1){let _=l==null;ko(f,p,l||s,a,o,E),_&&(s=[...s],le({}))}return D={get basename(){return i},get future(){return d},get state(){return y},get routes(){return s},get window(){return t},initialize:it,subscribe:lt,enableScrollRestoration:nn,navigate:Dt,fetch:Fe,revalidate:Zr,createHref:f=>e.history.createHref(f),encodeLocation:f=>e.history.encodeLocation(f),getFetcher:It,deleteFetcher:tt,dispose:Ge,getBlocker:dr,deleteBlocker:kt,patchRoutes:xe,_internalFetchControllers:I,_internalSetRoutes:ve,_internalSetStateDoNotUseOrYouWillBreakYourApp(f){le(f)}},D}function Lu(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function Tn(e,t,r,n,o,a){let s,l;if(o){s=[];for(let u of t)if(s.push(u),u.route.id===o){l=u;break}}else s=t,l=t[t.length-1];let i=Jr(n||".",Kr(s),Me(e.pathname,r)||e.pathname,a==="path");if(n==null&&(i.search=e.search,i.hash=e.hash),(n==null||n===""||n===".")&&l){let u=Hn(i.search);if(l.route.index&&!u)i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index";else if(!l.route.index&&u){let d=new URLSearchParams(i.search),c=d.getAll("index");d.delete("index"),c.filter(b=>b).forEach(b=>d.append("index",b));let h=d.toString();i.search=h?`?${h}`:""}}return r!=="/"&&(i.pathname=mu({basename:r,pathname:i.pathname})),st(i)}function Ao(e,t,r){if(!r||!Lu(r))return{path:t};if(r.formMethod&&!zu(r.formMethod))return{path:t,error:ke(405,{method:r.formMethod})};let n=()=>({path:t,error:ke(400,{type:"invalid-body"})}),a=(r.formMethod||"get").toUpperCase(),s=as(t);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!Te(a))return n();let c=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((h,[b,v])=>`${h}${b}=${v}
`,""):String(r.body);return{path:t,submission:{formMethod:a,formAction:s,formEncType:r.formEncType,formData:void 0,json:void 0,text:c}}}else if(r.formEncType==="application/json"){if(!Te(a))return n();try{let c=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:a,formAction:s,formEncType:r.formEncType,formData:void 0,json:c,text:void 0}}}catch{return n()}}}W(typeof FormData=="function","FormData is not available in this environment");let l,i;if(r.formData)l=On(r.formData),i=r.formData;else if(r.body instanceof FormData)l=On(r.body),i=r.body;else if(r.body instanceof URLSearchParams)l=r.body,i=jo(l);else if(r.body==null)l=new URLSearchParams,i=new FormData;else try{l=new URLSearchParams(r.body),i=jo(l)}catch{return n()}let u={formMethod:a,formAction:s,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:i,json:void 0,text:void 0};if(Te(u.formMethod))return{path:t,submission:u};let d=Ye(t);return e&&d.search&&Hn(d.search)&&l.append("index",""),d.search=`?${l}`,{path:st(d),submission:u}}function Io(e,t,r,n,o,a,s,l,i,u,d,c,h,b,v,R,m,w,C,S){let A=S?Le(S[1])?S[1].error:S[1].data:void 0,D=o.createURL(a.location),y=o.createURL(i),k;if(d&&a.errors){let X=Object.keys(a.errors)[0];k=s.findIndex(ee=>ee.route.id===X)}else if(S&&Le(S[1])){let X=S[0];k=s.findIndex(ee=>ee.route.id===X)-1}let j=S?S[1].statusCode:void 0,B=j&&j>=400,K={currentUrl:D,currentParams:a.matches[0]?.params||{},nextUrl:y,nextParams:s[0].params,...l,actionResult:A,actionStatus:j},te=s.map((X,ee)=>{let{route:ae}=X,I=null;if(k!=null&&ee>k?I=!1:ae.lazy?I=!0:ae.loader==null?I=!1:d?I=Pn(ae,a.loaderData,a.errors):Du(a.loaderData,a.matches[ee],X)&&(I=!0),I!==null)return Cn(r,n,e,X,u,t,I);let ne=B?!1:c||D.pathname+D.search===y.pathname+y.search||D.search!==y.search||Au(a.matches[ee],X),oe={...K,defaultShouldRevalidate:ne},se=Fr(X,oe);return Cn(r,n,e,X,u,t,se,oe)}),Se=[];return v.forEach((X,ee)=>{if(d||!s.some(Q=>Q.route.id===X.routeId)||b.has(ee))return;let ae=a.fetchers.get(ee),I=ae&&ae.state!=="idle"&&ae.data===void 0,ne=ot(m,X.path,w);if(!ne){if(C&&I)return;Se.push({key:ee,routeId:X.routeId,path:X.path,matches:null,match:null,request:null,controller:null});return}if(R.has(ee))return;let oe=Tr(ne,X.path),se=new AbortController,me=Et(o,X.path,se.signal),q=null;if(h.has(ee))h.delete(ee),q=Rt(r,n,me,ne,oe,u,t);else if(I)c&&(q=Rt(r,n,me,ne,oe,u,t));else{let Q={...K,defaultShouldRevalidate:B?!1:c};Fr(oe,Q)&&(q=Rt(r,n,me,ne,oe,u,t,Q))}q&&Se.push({key:ee,routeId:X.routeId,path:X.path,matches:q,match:oe,request:me,controller:se})}),{dsMatches:te,revalidatingFetchers:Se}}function Pn(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=t!=null&&e.id in t,o=r!=null&&r[e.id]!==void 0;return!n&&o?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!n&&!o}function Du(e,t,r){let n=!t||r.route.id!==t.route.id,o=!e.hasOwnProperty(r.route.id);return n||o}function Au(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function Fr(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function ko(e,t,r,n,o,a){let s;if(e){let u=n[e];W(u,`No route found to patch children into: routeId = ${e}`),u.children||(u.children=[]),s=u.children}else s=r;let l=[],i=[];if(t.forEach(u=>{let d=s.find(c=>es(u,c));d?i.push({existingRoute:d,newRoute:u}):l.push(u)}),l.length>0){let u=Yt(l,o,[e||"_","patch",String(s?.length||"0")],n);s.push(...u)}if(a&&i.length>0)for(let u=0;u<i.length;u++){let{existingRoute:d,newRoute:c}=i[u],h=d,[b]=Yt([c],o,[],{},!0);Object.assign(h,{element:b.element?b.element:h.element,errorElement:b.errorElement?b.errorElement:h.errorElement,hydrateFallbackElement:b.hydrateFallbackElement?b.hydrateFallbackElement:h.hydrateFallbackElement})}}function es(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,n)=>t.children?.some(o=>es(r,o))):!1}var Mo=new WeakMap,ts=({key:e,route:t,manifest:r,mapRouteProperties:n})=>{let o=r[t.id];if(W(o,"No route found in manifest"),!o.lazy||typeof o.lazy!="object")return;let a=o.lazy[e];if(!a)return;let s=Mo.get(o);s||(s={},Mo.set(o,s));let l=s[e];if(l)return l;let i=(async()=>{let u=Gl(e),c=o[e]!==void 0&&e!=="hasErrorBoundary";if(u)de(!u,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),s[e]=Promise.resolve();else if(c)de(!1,`Route "${o.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let h=await a();h!=null&&(Object.assign(o,{[e]:h}),Object.assign(o,n(o)))}typeof o.lazy=="object"&&(o.lazy[e]=void 0,Object.values(o.lazy).every(h=>h===void 0)&&(o.lazy=void 0))})();return s[e]=i,i},No=new WeakMap;function Iu(e,t,r,n,o){let a=r[e.id];if(W(a,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof e.lazy=="function"){let d=No.get(a);if(d)return{lazyRoutePromise:d,lazyHandlerPromise:d};let c=(async()=>{W(typeof e.lazy=="function","No lazy route function found");let h=await e.lazy(),b={};for(let v in h){let R=h[v];if(R===void 0)continue;let m=eu(v),C=a[v]!==void 0&&v!=="hasErrorBoundary";m?de(!m,"Route property "+v+" is not a supported property to be returned from a lazy route function. This property will be ignored."):C?de(!C,`Route "${a.id}" has a static property "${v}" defined but its lazy function is also returning a value for this property. The lazy route property "${v}" will be ignored.`):b[v]=R}Object.assign(a,b),Object.assign(a,{...n(a),lazy:void 0})})();return No.set(a,c),c.catch(()=>{}),{lazyRoutePromise:c,lazyHandlerPromise:c}}let s=Object.keys(e.lazy),l=[],i;for(let d of s){if(o&&o.includes(d))continue;let c=ts({key:d,route:e,manifest:r,mapRouteProperties:n});c&&(l.push(c),d===t&&(i=c))}let u=l.length>0?Promise.all(l).then(()=>{}):void 0;return u?.catch(()=>{}),i?.catch(()=>{}),{lazyRoutePromise:u,lazyHandlerPromise:i}}async function Fo(e){let t=e.matches.filter(o=>o.shouldLoad),r={};return(await Promise.all(t.map(o=>o.resolve()))).forEach((o,a)=>{r[t[a].route.id]=o}),r}async function ku(e){return e.matches.some(t=>t.route.unstable_middleware)?rs(e,!1,()=>Fo(e),(t,r)=>({[r]:{type:"error",result:t}})):Fo(e)}async function rs(e,t,r,n){let{matches:o,request:a,params:s,context:l}=e,i={handlerResult:void 0};try{let u=o.flatMap(c=>c.route.unstable_middleware?c.route.unstable_middleware.map(h=>[c.route.id,h]):[]),d=await ns({request:a,params:s,context:l},u,t,i,r);return t?d:i.handlerResult}catch(u){if(!i.middlewareError)throw u;let d=await n(i.middlewareError.error,i.middlewareError.routeId);return i.handlerResult?Object.assign(i.handlerResult,d):d}}async function ns(e,t,r,n,o,a=0){let{request:s}=e;if(s.signal.aborted)throw s.signal.reason?s.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${s.method} ${s.url}`);let l=t[a];if(!l)return n.handlerResult=await o(),n.handlerResult;let[i,u]=l,d=!1,c,h=async()=>{if(d)throw new Error("You may only call `next()` once per middleware");d=!0,await ns(e,t,r,n,o,a+1)};try{let b=await u({request:e.request,params:e.params,context:e.context},h);return d?b===void 0?c:b:h()}catch(b){throw n.middlewareError?n.middlewareError.error!==b&&(n.middlewareError={routeId:i,error:b}):n.middlewareError={routeId:i,error:b},b}}function os(e,t,r,n,o){let a=ts({key:"unstable_middleware",route:n.route,manifest:t,mapRouteProperties:e}),s=Iu(n.route,Te(r.method)?"action":"loader",t,e,o);return{middleware:a,route:s.lazyRoutePromise,handler:s.lazyHandlerPromise}}function Cn(e,t,r,n,o,a,s,l=null){let i=!1,u=os(e,t,r,n,o);return{...n,_lazyPromises:u,shouldLoad:s,unstable_shouldRevalidateArgs:l,unstable_shouldCallHandler(d){return i=!0,l?typeof d=="boolean"?Fr(n,{...l,defaultShouldRevalidate:d}):Fr(n,l):s},resolve(d){return i||s||d&&!Te(r.method)&&(n.route.lazy||n.route.loader)?Nu({request:r,match:n,lazyHandlerPromise:u?.handler,lazyRoutePromise:u?.route,handlerOverride:d,scopedContext:a}):Promise.resolve({type:"data",result:void 0})}}}function Rt(e,t,r,n,o,a,s,l=null){return n.map(i=>i.route.id!==o.route.id?{...i,shouldLoad:!1,unstable_shouldRevalidateArgs:l,unstable_shouldCallHandler:()=>!1,_lazyPromises:os(e,t,r,i,a),resolve:()=>Promise.resolve({type:"data",result:void 0})}:Cn(e,t,r,i,a,s,!0,l))}async function Mu(e,t,r,n,o,a){r.some(u=>u._lazyPromises?.middleware)&&await Promise.all(r.map(u=>u._lazyPromises?.middleware));let s={request:t,params:r[0].params,context:o,matches:r},i=await e({...s,fetcherKey:n,unstable_runClientMiddleware:u=>{let d=s;return rs(d,!1,()=>u({...d,fetcherKey:n,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(c,h)=>({[h]:{type:"error",result:c}}))}});try{await Promise.all(r.flatMap(u=>[u._lazyPromises?.handler,u._lazyPromises?.route]))}catch{}return i}async function Nu({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:n,handlerOverride:o,scopedContext:a}){let s,l,i=Te(e.method),u=i?"action":"loader",d=c=>{let h,b=new Promise((m,w)=>h=w);l=()=>h(),e.signal.addEventListener("abort",l);let v=m=>typeof c!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${u}" [routeId: ${t.route.id}]`)):c({request:e,params:t.params,context:a},...m!==void 0?[m]:[]),R=(async()=>{try{return{type:"data",result:await(o?o(w=>v(w)):v())}}catch(m){return{type:"error",result:m}}})();return Promise.race([R,b])};try{let c=i?t.route.action:t.route.loader;if(r||n)if(c){let h,[b]=await Promise.all([d(c).catch(v=>{h=v}),r,n]);if(h!==void 0)throw h;s=b}else{await r;let h=i?t.route.action:t.route.loader;if(h)[s]=await Promise.all([d(h),n]);else if(u==="action"){let b=new URL(e.url),v=b.pathname+b.search;throw ke(405,{method:e.method,pathname:v,routeId:t.route.id})}else return{type:"data",result:void 0}}else if(c)s=await d(c);else{let h=new URL(e.url),b=h.pathname+h.search;throw ke(404,{pathname:b})}}catch(c){return{type:"error",result:c}}finally{l&&e.signal.removeEventListener("abort",l)}return s}async function Fu(e){let{result:t,type:r}=e;if(ss(t)){let n;try{let o=t.headers.get("Content-Type");o&&/\bapplication\/json\b/.test(o)?t.body==null?n=null:n=await t.json():n=await t.text()}catch(o){return{type:"error",error:o}}return r==="error"?{type:"error",error:new Nr(t.status,t.statusText,n),statusCode:t.status,headers:t.headers}:{type:"data",data:n,statusCode:t.status,headers:t.headers}}return r==="error"?Vo(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new Nr(t.init?.status||500,void 0,t.data),statusCode:Xt(t)?t.status:void 0,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:Xt(t)?t.status:void 0}:Vo(t)?{type:"data",data:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function Uu(e,t,r,n,o){let a=e.headers.get("Location");if(W(a,"Redirects returned/thrown from loaders/actions must have a Location header"),!zn(a)){let s=n.slice(0,n.findIndex(l=>l.route.id===r)+1);a=Tn(new URL(t.url),s,o,a),e.headers.set("Location",a)}return e}function Uo(e,t,r){if(zn(e)){let n=e,o=n.startsWith("//")?new URL(t.protocol+n):new URL(n),a=Me(o.pathname,r)!=null;if(o.origin===t.origin&&a)return o.pathname+o.search+o.hash}return e}function Et(e,t,r,n){let o=e.createURL(as(t)).toString(),a={signal:r};if(n&&Te(n.formMethod)){let{formMethod:s,formEncType:l}=n;a.method=s.toUpperCase(),l==="application/json"?(a.headers=new Headers({"Content-Type":l}),a.body=JSON.stringify(n.json)):l==="text/plain"?a.body=n.text:l==="application/x-www-form-urlencoded"&&n.formData?a.body=On(n.formData):a.body=n.formData}return new Request(o,a)}function On(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,typeof n=="string"?n:n.name);return t}function jo(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function ju(e,t,r,n=!1,o=!1){let a={},s=null,l,i=!1,u={},d=r&&Le(r[1])?r[1].error:void 0;return e.forEach(c=>{if(!(c.route.id in t))return;let h=c.route.id,b=t[h];if(W(!pt(b),"Cannot handle redirect results in processLoaderData"),Le(b)){let v=b.error;if(d!==void 0&&(v=d,d=void 0),s=s||{},o)s[h]=v;else{let R=ft(e,h);s[R.route.id]==null&&(s[R.route.id]=v)}n||(a[h]=Za),i||(i=!0,l=Xt(b.error)?b.error.status:500),b.headers&&(u[h]=b.headers)}else a[h]=b.data,b.statusCode&&b.statusCode!==200&&!i&&(l=b.statusCode),b.headers&&(u[h]=b.headers)}),d!==void 0&&r&&(s={[r[0]]:d},r[2]&&(a[r[2]]=void 0)),{loaderData:a,errors:s,statusCode:l||200,loaderHeaders:u}}function Bo(e,t,r,n,o,a){let{loaderData:s,errors:l}=ju(t,r,n);return o.filter(i=>!i.matches||i.matches.some(u=>u.shouldLoad)).forEach(i=>{let{key:u,match:d,controller:c}=i,h=a[u];if(W(h,"Did not find corresponding fetcher result"),!(c&&c.signal.aborted))if(Le(h)){let b=ft(e.matches,d?.route.id);l&&l[b.route.id]||(l={...l,[b.route.id]:h.error}),e.fetchers.delete(u)}else if(pt(h))W(!1,"Unhandled fetcher revalidation redirect");else{let b=nt(h.data);e.fetchers.set(u,b)}}),{loaderData:s,errors:l}}function $o(e,t,r,n){let o=Object.entries(t).filter(([,a])=>a!==Za).reduce((a,[s,l])=>(a[s]=l,a),{});for(let a of r){let s=a.route.id;if(!t.hasOwnProperty(s)&&e.hasOwnProperty(s)&&a.route.loader&&(o[s]=e[s]),n&&n.hasOwnProperty(s))break}return o}function zo(e){return e?Le(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function ft(e,t){return(t?e.slice(0,e.findIndex(n=>n.route.id===t)+1):[...e]).reverse().find(n=>n.route.hasErrorBoundary===!0)||e[0]}function Ho(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function ke(e,{pathname:t,routeId:r,method:n,type:o,message:a}={}){let s="Unknown Server Error",l="Unknown @remix-run/router error";return e===400?(s="Bad Request",n&&t&&r?l=`You made a ${n} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:o==="invalid-body"&&(l="Unable to encode submission body")):e===403?(s="Forbidden",l=`Route "${r}" does not match URL "${t}"`):e===404?(s="Not Found",l=`No route matches URL "${t}"`):e===405&&(s="Method Not Allowed",n&&t&&r?l=`You made a ${n.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:n&&(l=`Invalid request method "${n.toUpperCase()}"`)),new Nr(e||500,s,new Error(l),!0)}function vr(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[n,o]=t[r];if(pt(o))return{key:n,result:o}}}function as(e){let t=typeof e=="string"?Ye(e):e;return st({...t,hash:""})}function Bu(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function $u(e){return ss(e.result)&&Su.has(e.result.status)}function Le(e){return e.type==="error"}function pt(e){return(e&&e.type)==="redirect"}function Vo(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function ss(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function zu(e){return Ru.has(e.toUpperCase())}function Te(e){return Eu.has(e.toUpperCase())}function Hn(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function Tr(e,t){let r=typeof t=="string"?Ye(t).search:t.search;if(e[e.length-1].route.index&&Hn(r||""))return e[e.length-1];let n=Ya(e);return n[n.length-1]}function qo(e){let{formMethod:t,formAction:r,formEncType:n,text:o,formData:a,json:s}=e;if(!(!t||!r||!n)){if(o!=null)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:o};if(a!=null)return{formMethod:t,formAction:r,formEncType:n,formData:a,json:void 0,text:void 0};if(s!==void 0)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:s,text:void 0}}}function fn(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function Hu(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function $t(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function Vu(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function nt(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function qu(e,t){try{let r=e.sessionStorage.getItem(Ga);if(r){let n=JSON.parse(r);for(let[o,a]of Object.entries(n||{}))a&&Array.isArray(a)&&t.set(o,new Set(a||[]))}}catch{}}function Wu(e,t){if(t.size>0){let r={};for(let[n,o]of t)r[n]=[...o];try{e.sessionStorage.setItem(Ga,JSON.stringify(r))}catch(n){de(!1,`Failed to save applied view transitions in sessionStorage (${n}).`)}}}function Ku(){let e,t,r=new Promise((n,o)=>{e=async a=>{n(a);try{await r}catch{}},t=async a=>{o(a);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}var yt=g.createContext(null);yt.displayName="DataRouter";var nr=g.createContext(null);nr.displayName="DataRouterState";g.createContext(!1);var Vn=g.createContext({isTransitioning:!1});Vn.displayName="ViewTransition";var is=g.createContext(new Map);is.displayName="Fetchers";var Ju=g.createContext(null);Ju.displayName="Await";var ze=g.createContext(null);ze.displayName="Navigation";var Qr=g.createContext(null);Qr.displayName="Location";var He=g.createContext({outlet:null,matches:[],isDataRoute:!1});He.displayName="Route";var qn=g.createContext(null);qn.displayName="RouteError";function Qu(e,{relative:t}={}){W(Ct(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:n}=g.useContext(ze),{hash:o,pathname:a,search:s}=or(e,{relative:t}),l=a;return r!=="/"&&(l=a==="/"?r:Ke([r,a])),n.createHref({pathname:l,search:s,hash:o})}function Ct(){return g.useContext(Qr)!=null}function Xe(){return W(Ct(),"useLocation() may be used only in the context of a <Router> component."),g.useContext(Qr).location}var ls="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function us(e){g.useContext(ze).static||g.useLayoutEffect(e)}function Yr(){let{isDataRoute:e}=g.useContext(He);return e?cc():Yu()}function Yu(){W(Ct(),"useNavigate() may be used only in the context of a <Router> component.");let e=g.useContext(yt),{basename:t,navigator:r}=g.useContext(ze),{matches:n}=g.useContext(He),{pathname:o}=Xe(),a=JSON.stringify(Kr(n)),s=g.useRef(!1);return us(()=>{s.current=!0}),g.useCallback((i,u={})=>{if(de(s.current,ls),!s.current)return;if(typeof i=="number"){r.go(i);return}let d=Jr(i,JSON.parse(a),o,u.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:Ke([t,d.pathname])),(u.replace?r.replace:r.push)(d,u.state,u)},[t,r,a,o,e])}var Xu=g.createContext(null);function Gu(e){let t=g.useContext(He).outlet;return t&&g.createElement(Xu.Provider,{value:e},t)}function or(e,{relative:t}={}){let{matches:r}=g.useContext(He),{pathname:n}=Xe(),o=JSON.stringify(Kr(r));return g.useMemo(()=>Jr(e,JSON.parse(o),n,t==="path"),[e,o,n,t])}function Zu(e,t,r,n){W(Ct(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o}=g.useContext(ze),{matches:a}=g.useContext(He),s=a[a.length-1],l=s?s.params:{},i=s?s.pathname:"/",u=s?s.pathnameBase:"/",d=s&&s.route;{let w=d&&d.path||"";ds(i,!d||w.endsWith("*")||w.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${i}" (under <Route path="${w}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${w}"> to <Route path="${w==="/"?"*":`${w}/*`}">.`)}let c=Xe(),h;h=c;let b=h.pathname||"/",v=b;if(u!=="/"){let w=u.replace(/^\//,"").split("/");v="/"+b.replace(/^\//,"").split("/").slice(w.length).join("/")}let R=ot(e,{pathname:v});return de(d||R!=null,`No routes matched location "${h.pathname}${h.search}${h.hash}" `),de(R==null||R[R.length-1].route.element!==void 0||R[R.length-1].route.Component!==void 0||R[R.length-1].route.lazy!==void 0,`Matched leaf route at location "${h.pathname}${h.search}${h.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),oc(R&&R.map(w=>Object.assign({},w,{params:Object.assign({},l,w.params),pathname:Ke([u,o.encodeLocation?o.encodeLocation(w.pathname).pathname:w.pathname]),pathnameBase:w.pathnameBase==="/"?u:Ke([u,o.encodeLocation?o.encodeLocation(w.pathnameBase).pathname:w.pathnameBase])})),a,r,n)}function ec(){let e=uc(),t=Xt(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:n},a={padding:"2px 4px",backgroundColor:n},s=null;return console.error("Error handled by React Router default ErrorBoundary:",e),s=g.createElement(g.Fragment,null,g.createElement("p",null,"💿 Hey developer 👋"),g.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",g.createElement("code",{style:a},"ErrorBoundary")," or"," ",g.createElement("code",{style:a},"errorElement")," prop on your route.")),g.createElement(g.Fragment,null,g.createElement("h2",null,"Unexpected Application Error!"),g.createElement("h3",{style:{fontStyle:"italic"}},t),r?g.createElement("pre",{style:o},r):null,s)}var tc=g.createElement(ec,null),rc=class extends g.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?g.createElement(He.Provider,{value:this.props.routeContext},g.createElement(qn.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function nc({routeContext:e,match:t,children:r}){let n=g.useContext(yt);return n&&n.static&&n.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=t.route.id),g.createElement(He.Provider,{value:e},r)}function oc(e,t=[],r=null,n=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let o=e,a=r?.errors;if(a!=null){let i=o.findIndex(u=>u.route.id&&a?.[u.route.id]!==void 0);W(i>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(a).join(",")}`),o=o.slice(0,Math.min(o.length,i+1))}let s=!1,l=-1;if(r)for(let i=0;i<o.length;i++){let u=o[i];if((u.route.HydrateFallback||u.route.hydrateFallbackElement)&&(l=i),u.route.id){let{loaderData:d,errors:c}=r,h=u.route.loader&&!d.hasOwnProperty(u.route.id)&&(!c||c[u.route.id]===void 0);if(u.route.lazy||h){s=!0,l>=0?o=o.slice(0,l+1):o=[o[0]];break}}}return o.reduceRight((i,u,d)=>{let c,h=!1,b=null,v=null;r&&(c=a&&u.route.id?a[u.route.id]:void 0,b=u.route.errorElement||tc,s&&(l<0&&d===0?(ds("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),h=!0,v=null):l===d&&(h=!0,v=u.route.hydrateFallbackElement||null)));let R=t.concat(o.slice(0,d+1)),m=()=>{let w;return c?w=b:h?w=v:u.route.Component?w=g.createElement(u.route.Component,null):u.route.element?w=u.route.element:w=i,g.createElement(nc,{match:u,routeContext:{outlet:i,matches:R,isDataRoute:r!=null},children:w})};return r&&(u.route.ErrorBoundary||u.route.errorElement||d===0)?g.createElement(rc,{location:r.location,revalidation:r.revalidation,component:b,error:c,children:m(),routeContext:{outlet:null,matches:R,isDataRoute:!0}}):m()},null)}function Wn(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function ac(e){let t=g.useContext(yt);return W(t,Wn(e)),t}function cs(e){let t=g.useContext(nr);return W(t,Wn(e)),t}function sc(e){let t=g.useContext(He);return W(t,Wn(e)),t}function Kn(e){let t=sc(e),r=t.matches[t.matches.length-1];return W(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function ic(){return Kn("useRouteId")}function lc(){return cs("useNavigation").navigation}function uc(){let e=g.useContext(qn),t=cs("useRouteError"),r=Kn("useRouteError");return e!==void 0?e:t.errors?.[r]}function cc(){let{router:e}=ac("useNavigate"),t=Kn("useNavigate"),r=g.useRef(!1);return us(()=>{r.current=!0}),g.useCallback(async(o,a={})=>{de(r.current,ls),r.current&&(typeof o=="number"?e.navigate(o):await e.navigate(o,{fromRouteId:t,...a}))},[e,t])}var Wo={};function ds(e,t,r){!t&&!Wo[e]&&(Wo[e]=!0,de(!1,r))}var Ko={};function Jo(e,t){!e&&!Ko[t]&&(Ko[t]=!0,console.warn(t))}function dc(e){let t={hasErrorBoundary:e.hasErrorBoundary||e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&(e.element&&de(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:g.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&de(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:g.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&de(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:g.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}var fc=["HydrateFallback","hydrateFallbackElement"],hc=class{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",e(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",t(r))}})}};function pc({router:e,flushSync:t}){let[r,n]=g.useState(e.state),[o,a]=g.useState(),[s,l]=g.useState({isTransitioning:!1}),[i,u]=g.useState(),[d,c]=g.useState(),[h,b]=g.useState(),v=g.useRef(new Map),R=g.useCallback((S,{deletedFetchers:A,flushSync:D,viewTransitionOpts:y})=>{S.fetchers.forEach((j,B)=>{j.data!==void 0&&v.current.set(B,j.data)}),A.forEach(j=>v.current.delete(j)),Jo(D===!1||t!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let k=e.window!=null&&e.window.document!=null&&typeof e.window.document.startViewTransition=="function";if(Jo(y==null||k,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!y||!k){t&&D?t(()=>n(S)):g.startTransition(()=>n(S));return}if(t&&D){t(()=>{d&&(i&&i.resolve(),d.skipTransition()),l({isTransitioning:!0,flushSync:!0,currentLocation:y.currentLocation,nextLocation:y.nextLocation})});let j=e.window.document.startViewTransition(()=>{t(()=>n(S))});j.finished.finally(()=>{t(()=>{u(void 0),c(void 0),a(void 0),l({isTransitioning:!1})})}),t(()=>c(j));return}d?(i&&i.resolve(),d.skipTransition(),b({state:S,currentLocation:y.currentLocation,nextLocation:y.nextLocation})):(a(S),l({isTransitioning:!0,flushSync:!1,currentLocation:y.currentLocation,nextLocation:y.nextLocation}))},[e.window,t,d,i]);g.useLayoutEffect(()=>e.subscribe(R),[e,R]),g.useEffect(()=>{s.isTransitioning&&!s.flushSync&&u(new hc)},[s]),g.useEffect(()=>{if(i&&o&&e.window){let S=o,A=i.promise,D=e.window.document.startViewTransition(async()=>{g.startTransition(()=>n(S)),await A});D.finished.finally(()=>{u(void 0),c(void 0),a(void 0),l({isTransitioning:!1})}),c(D)}},[o,i,e.window]),g.useEffect(()=>{i&&o&&r.location.key===o.location.key&&i.resolve()},[i,d,r.location,o]),g.useEffect(()=>{!s.isTransitioning&&h&&(a(h.state),l({isTransitioning:!0,flushSync:!1,currentLocation:h.currentLocation,nextLocation:h.nextLocation}),b(void 0))},[s.isTransitioning,h]);let m=g.useMemo(()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:S=>e.navigate(S),push:(S,A,D)=>e.navigate(S,{state:A,preventScrollReset:D?.preventScrollReset}),replace:(S,A,D)=>e.navigate(S,{replace:!0,state:A,preventScrollReset:D?.preventScrollReset})}),[e]),w=e.basename||"/",C=g.useMemo(()=>({router:e,navigator:m,static:!1,basename:w}),[e,m,w]);return g.createElement(g.Fragment,null,g.createElement(yt.Provider,{value:C},g.createElement(nr.Provider,{value:r},g.createElement(is.Provider,{value:v.current},g.createElement(Vn.Provider,{value:s},g.createElement(vc,{basename:w,location:r.location,navigationType:r.historyAction,navigator:m},g.createElement(mc,{routes:e.routes,future:e.future,state:r})))))),null)}var mc=g.memo(gc);function gc({routes:e,future:t,state:r}){return Zu(e,void 0,r,t)}function fs({to:e,replace:t,state:r,relative:n}){W(Ct(),"<Navigate> may be used only in the context of a <Router> component.");let{static:o}=g.useContext(ze);de(!o,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:a}=g.useContext(He),{pathname:s}=Xe(),l=Yr(),i=Jr(e,Kr(a),s,n==="path"),u=JSON.stringify(i);return g.useEffect(()=>{l(JSON.parse(u),{replace:t,state:r,relative:n})},[l,u,n,t,r]),null}function yc(e){return Gu(e.context)}function vc({basename:e="/",children:t=null,location:r,navigationType:n="POP",navigator:o,static:a=!1}){W(!Ct(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let s=e.replace(/^\/*/,"/"),l=g.useMemo(()=>({basename:s,navigator:o,static:a,future:{}}),[s,o,a]);typeof r=="string"&&(r=Ye(r));let{pathname:i="/",search:u="",hash:d="",state:c=null,key:h="default"}=r,b=g.useMemo(()=>{let v=Me(i,s);return v==null?null:{location:{pathname:v,search:u,hash:d,state:c,key:h},navigationType:n}},[s,i,u,d,c,h,n]);return de(b!=null,`<Router basename="${s}"> is not able to match the URL "${i}${u}${d}" because it does not start with the basename, so the <Router> won't render anything.`),b==null?null:g.createElement(ze.Provider,{value:l},g.createElement(Qr.Provider,{children:t,value:b}))}var Pr="get",Cr="application/x-www-form-urlencoded";function Xr(e){return e!=null&&typeof e.tagName=="string"}function bc(e){return Xr(e)&&e.tagName.toLowerCase()==="button"}function wc(e){return Xr(e)&&e.tagName.toLowerCase()==="form"}function Ec(e){return Xr(e)&&e.tagName.toLowerCase()==="input"}function xc(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Rc(e,t){return e.button===0&&(!t||t==="_self")&&!xc(e)}var br=null;function Sc(){if(br===null)try{new FormData(document.createElement("form"),0),br=!1}catch{br=!0}return br}var _c=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function hn(e){return e!=null&&!_c.has(e)?(de(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Cr}"`),null):e}function Tc(e,t){let r,n,o,a,s;if(wc(e)){let l=e.getAttribute("action");n=l?Me(l,t):null,r=e.getAttribute("method")||Pr,o=hn(e.getAttribute("enctype"))||Cr,a=new FormData(e)}else if(bc(e)||Ec(e)&&(e.type==="submit"||e.type==="image")){let l=e.form;if(l==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let i=e.getAttribute("formaction")||l.getAttribute("action");if(n=i?Me(i,t):null,r=e.getAttribute("formmethod")||l.getAttribute("method")||Pr,o=hn(e.getAttribute("formenctype"))||hn(l.getAttribute("enctype"))||Cr,a=new FormData(l,e),!Sc()){let{name:u,type:d,value:c}=e;if(d==="image"){let h=u?`${u}.`:"";a.append(`${h}x`,"0"),a.append(`${h}y`,"0")}else u&&a.append(u,c)}}else{if(Xr(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=Pr,n=null,o=Cr,s=e}return a&&o==="text/plain"&&(s=a,a=void 0),{action:n,method:r.toLowerCase(),encType:o,formData:a,body:s}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function Jn(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Pc(e,t,r){let n=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return n.pathname==="/"?n.pathname=`_root.${r}`:t&&Me(n.pathname,t)==="/"?n.pathname=`${t.replace(/\/$/,"")}/_root.${r}`:n.pathname=`${n.pathname.replace(/\/$/,"")}.${r}`,n}async function Cc(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Oc(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function Lc(e,t,r){let n=await Promise.all(e.map(async o=>{let a=t.routes[o.route.id];if(a){let s=await Cc(a,r);return s.links?s.links():[]}return[]}));return kc(n.flat(1).filter(Oc).filter(o=>o.rel==="stylesheet"||o.rel==="preload").map(o=>o.rel==="stylesheet"?{...o,rel:"prefetch",as:"style"}:{...o,rel:"prefetch"}))}function Qo(e,t,r,n,o,a){let s=(i,u)=>r[u]?i.route.id!==r[u].route.id:!0,l=(i,u)=>r[u].pathname!==i.pathname||r[u].route.path?.endsWith("*")&&r[u].params["*"]!==i.params["*"];return a==="assets"?t.filter((i,u)=>s(i,u)||l(i,u)):a==="data"?t.filter((i,u)=>{let d=n.routes[i.route.id];if(!d||!d.hasLoader)return!1;if(s(i,u)||l(i,u))return!0;if(i.route.shouldRevalidate){let c=i.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:r[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:i.params,defaultShouldRevalidate:!0});if(typeof c=="boolean")return c}return!0}):[]}function Dc(e,t,{includeHydrateFallback:r}={}){return Ac(e.map(n=>{let o=t.routes[n.route.id];if(!o)return[];let a=[o.module];return o.clientActionModule&&(a=a.concat(o.clientActionModule)),o.clientLoaderModule&&(a=a.concat(o.clientLoaderModule)),r&&o.hydrateFallbackModule&&(a=a.concat(o.hydrateFallbackModule)),o.imports&&(a=a.concat(o.imports)),a}).flat(1))}function Ac(e){return[...new Set(e)]}function Ic(e){let t={},r=Object.keys(e).sort();for(let n of r)t[n]=e[n];return t}function kc(e,t){let r=new Set;return new Set(t),e.reduce((n,o)=>{let a=JSON.stringify(Ic(o));return r.has(a)||(r.add(a),n.push({key:a,link:o})),n},[])}function hs(){let e=g.useContext(yt);return Jn(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function Mc(){let e=g.useContext(nr);return Jn(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var Qn=g.createContext(void 0);Qn.displayName="FrameworkContext";function ps(){let e=g.useContext(Qn);return Jn(e,"You must render this element inside a <HydratedRouter> element"),e}function Nc(e,t){let r=g.useContext(Qn),[n,o]=g.useState(!1),[a,s]=g.useState(!1),{onFocus:l,onBlur:i,onMouseEnter:u,onMouseLeave:d,onTouchStart:c}=t,h=g.useRef(null);g.useEffect(()=>{if(e==="render"&&s(!0),e==="viewport"){let R=w=>{w.forEach(C=>{s(C.isIntersecting)})},m=new IntersectionObserver(R,{threshold:.5});return h.current&&m.observe(h.current),()=>{m.disconnect()}}},[e]),g.useEffect(()=>{if(n){let R=setTimeout(()=>{s(!0)},100);return()=>{clearTimeout(R)}}},[n]);let b=()=>{o(!0)},v=()=>{o(!1),s(!1)};return r?e!=="intent"?[a,h,{}]:[a,h,{onFocus:zt(l,b),onBlur:zt(i,v),onMouseEnter:zt(u,b),onMouseLeave:zt(d,v),onTouchStart:zt(c,b)}]:[!1,h,{}]}function zt(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function Fc({page:e,...t}){let{router:r}=hs(),n=g.useMemo(()=>ot(r.routes,e,r.basename),[r.routes,e,r.basename]);return n?g.createElement(jc,{page:e,matches:n,...t}):null}function Uc(e){let{manifest:t,routeModules:r}=ps(),[n,o]=g.useState([]);return g.useEffect(()=>{let a=!1;return Lc(e,t,r).then(s=>{a||o(s)}),()=>{a=!0}},[e,t,r]),n}function jc({page:e,matches:t,...r}){let n=Xe(),{manifest:o,routeModules:a}=ps(),{basename:s}=hs(),{loaderData:l,matches:i}=Mc(),u=g.useMemo(()=>Qo(e,t,i,o,n,"data"),[e,t,i,o,n]),d=g.useMemo(()=>Qo(e,t,i,o,n,"assets"),[e,t,i,o,n]),c=g.useMemo(()=>{if(e===n.pathname+n.search+n.hash)return[];let v=new Set,R=!1;if(t.forEach(w=>{let C=o.routes[w.route.id];!C||!C.hasLoader||(!u.some(S=>S.route.id===w.route.id)&&w.route.id in l&&a[w.route.id]?.shouldRevalidate||C.hasClientLoader?R=!0:v.add(w.route.id))}),v.size===0)return[];let m=Pc(e,s,"data");return R&&v.size>0&&m.searchParams.set("_routes",t.filter(w=>v.has(w.route.id)).map(w=>w.route.id).join(",")),[m.pathname+m.search]},[s,l,n,o,u,t,e,a]),h=g.useMemo(()=>Dc(d,o),[d,o]),b=Uc(d);return g.createElement(g.Fragment,null,c.map(v=>g.createElement("link",{key:v,rel:"prefetch",as:"fetch",href:v,...r})),h.map(v=>g.createElement("link",{key:v,rel:"modulepreload",href:v,...r})),b.map(({key:v,link:R})=>g.createElement("link",{key:v,...R})))}function Bc(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var ms=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{ms&&(window.__reactRouterVersion="7.7.1")}catch{}function $c(e,t){return Ou({basename:t?.basename,unstable_getContext:t?.unstable_getContext,future:t?.future,history:Jl({window:t?.window}),hydrationData:zc(),routes:e,mapRouteProperties:dc,hydrationRouteProperties:fc,dataStrategy:t?.dataStrategy,patchRoutesOnNavigation:t?.patchRoutesOnNavigation,window:t?.window}).initialize()}function zc(){let e=window?.__staticRouterHydrationData;return e&&e.errors&&(e={...e,errors:Hc(e.errors)}),e}function Hc(e){if(!e)return null;let t=Object.entries(e),r={};for(let[n,o]of t)if(o&&o.__type==="RouteErrorResponse")r[n]=new Nr(o.status,o.statusText,o.data,o.internal===!0);else if(o&&o.__type==="Error"){if(o.__subType){let a=window[o.__subType];if(typeof a=="function")try{let s=new a(o.message);s.stack="",r[n]=s}catch{}}if(r[n]==null){let a=new Error(o.message);a.stack="",r[n]=a}}else r[n]=o;return r}var gs=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ys=g.forwardRef(function({onClick:t,discover:r="render",prefetch:n="none",relative:o,reloadDocument:a,replace:s,state:l,target:i,to:u,preventScrollReset:d,viewTransition:c,...h},b){let{basename:v}=g.useContext(ze),R=typeof u=="string"&&gs.test(u),m,w=!1;if(typeof u=="string"&&R&&(m=u,ms))try{let B=new URL(window.location.href),K=u.startsWith("//")?new URL(B.protocol+u):new URL(u),te=Me(K.pathname,v);K.origin===B.origin&&te!=null?u=te+K.search+K.hash:w=!0}catch{de(!1,`<Link to="${u}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let C=Qu(u,{relative:o}),[S,A,D]=Nc(n,h),y=Kc(u,{replace:s,state:l,target:i,preventScrollReset:d,relative:o,viewTransition:c});function k(B){t&&t(B),B.defaultPrevented||y(B)}let j=g.createElement("a",{...h,...D,href:m||C,onClick:w||a?t:k,ref:Bc(b,A),target:i,"data-discover":!R&&r==="render"?"true":void 0});return S&&!R?g.createElement(g.Fragment,null,j,g.createElement(Fc,{page:C})):j});ys.displayName="Link";var Vc=g.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:n="",end:o=!1,style:a,to:s,viewTransition:l,children:i,...u},d){let c=or(s,{relative:u.relative}),h=Xe(),b=g.useContext(nr),{navigator:v,basename:R}=g.useContext(ze),m=b!=null&&Gc(c)&&l===!0,w=v.encodeLocation?v.encodeLocation(c).pathname:c.pathname,C=h.pathname,S=b&&b.navigation&&b.navigation.location?b.navigation.location.pathname:null;r||(C=C.toLowerCase(),S=S?S.toLowerCase():null,w=w.toLowerCase()),S&&R&&(S=Me(S,R)||S);const A=w!=="/"&&w.endsWith("/")?w.length-1:w.length;let D=C===w||!o&&C.startsWith(w)&&C.charAt(A)==="/",y=S!=null&&(S===w||!o&&S.startsWith(w)&&S.charAt(w.length)==="/"),k={isActive:D,isPending:y,isTransitioning:m},j=D?t:void 0,B;typeof n=="function"?B=n(k):B=[n,D?"active":null,y?"pending":null,m?"transitioning":null].filter(Boolean).join(" ");let K=typeof a=="function"?a(k):a;return g.createElement(ys,{...u,"aria-current":j,className:B,ref:d,style:K,to:s,viewTransition:l},typeof i=="function"?i(k):i)});Vc.displayName="NavLink";var qc=g.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:n,replace:o,state:a,method:s=Pr,action:l,onSubmit:i,relative:u,preventScrollReset:d,viewTransition:c,...h},b)=>{let v=Yc(),R=Xc(l,{relative:u}),m=s.toLowerCase()==="get"?"get":"post",w=typeof l=="string"&&gs.test(l),C=S=>{if(i&&i(S),S.defaultPrevented)return;S.preventDefault();let A=S.nativeEvent.submitter,D=A?.getAttribute("formmethod")||s;v(A||S.currentTarget,{fetcherKey:t,method:D,navigate:r,replace:o,state:a,relative:u,preventScrollReset:d,viewTransition:c})};return g.createElement("form",{ref:b,method:m,action:R,onSubmit:n?i:C,...h,"data-discover":!w&&e==="render"?"true":void 0})});qc.displayName="Form";function Wc(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function vs(e){let t=g.useContext(yt);return W(t,Wc(e)),t}function Kc(e,{target:t,replace:r,state:n,preventScrollReset:o,relative:a,viewTransition:s}={}){let l=Yr(),i=Xe(),u=or(e,{relative:a});return g.useCallback(d=>{if(Rc(d,t)){d.preventDefault();let c=r!==void 0?r:st(i)===st(u);l(e,{replace:c,state:n,preventScrollReset:o,relative:a,viewTransition:s})}},[i,l,u,r,n,t,e,o,a,s])}var Jc=0,Qc=()=>`__${String(++Jc)}__`;function Yc(){let{router:e}=vs("useSubmit"),{basename:t}=g.useContext(ze),r=ic();return g.useCallback(async(n,o={})=>{let{action:a,method:s,encType:l,formData:i,body:u}=Tc(n,t);if(o.navigate===!1){let d=o.fetcherKey||Qc();await e.fetch(d,r,o.action||a,{preventScrollReset:o.preventScrollReset,formData:i,body:u,formMethod:o.method||s,formEncType:o.encType||l,flushSync:o.flushSync})}else await e.navigate(o.action||a,{preventScrollReset:o.preventScrollReset,formData:i,body:u,formMethod:o.method||s,formEncType:o.encType||l,replace:o.replace,state:o.state,fromRouteId:r,flushSync:o.flushSync,viewTransition:o.viewTransition})},[e,t,r])}function Xc(e,{relative:t}={}){let{basename:r}=g.useContext(ze),n=g.useContext(He);W(n,"useFormAction must be used inside a RouteContext");let[o]=n.matches.slice(-1),a={...or(e||".",{relative:t})},s=Xe();if(e==null){a.search=s.search;let l=new URLSearchParams(a.search),i=l.getAll("index");if(i.some(d=>d==="")){l.delete("index"),i.filter(c=>c).forEach(c=>l.append("index",c));let d=l.toString();a.search=d?`?${d}`:""}}return(!e||e===".")&&o.route.index&&(a.search=a.search?a.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(a.pathname=a.pathname==="/"?r:Ke([r,a.pathname])),st(a)}function Gc(e,{relative:t}={}){let r=g.useContext(Vn);W(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:n}=vs("useViewTransitionState"),o=or(e,{relative:t});if(!r.isTransitioning)return!1;let a=Me(r.currentLocation.pathname,n)||r.currentLocation.pathname,s=Me(r.nextLocation.pathname,n)||r.nextLocation.pathname;return Mr(o.pathname,s)!=null||Mr(o.pathname,a)!=null}const Yn=pe.create({baseURL:"http://localhost:5500",timeout:5e3});Yn.interceptors.request.use(e=>{const t=xt.getState().userToken.token;return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e));Yn.interceptors.response.use(e=>e,e=>{if(e.response?.status===401){const{clearUserToken:t}=bs(),r=Yr();t(),r("/")}return Promise.reject(e)});class Zc{get(t){return this.request({...t,method:"GET"})}post(t){return this.request({...t,method:"POST"})}put(t){return this.request({...t,method:"PUT"})}patch(t){return this.request({...t,method:"PATCH"})}delete(t){return this.request({...t,method:"DELETE"})}request(t){return new Promise((r,n)=>{Yn.request(t).then(o=>{r(o.data)}).catch(o=>{o.response&&o.response.data?n({...o,data:o.response.data,message:"request传递了一个错误"}):n(o)})})}}const Ot=new Zc,Lt={user:{signIn:"api/authorizations",userProfile:"api/user/profile",userList:"api/users",deleteUsers:"api/users",updateUsers:"api/user",createUser:"api/user"},permission:{getPermissionList:"api/permission",getPermissionById:"api/permission",createPermissionNode:"api/permission",deletePermissionNode:"api/permission",updatePermssionNode:"api/permsission",getRootPermissionList:"api/permissions/catalogue",getNextRootId:"api/permission/next-root-id",getNextChildId:"api/permission/next-child-id"},role:{getRoleList:"api/roles",createRole:"api/role",getRole:"api/role",updateRole:"api/role",deleteRole:"api/role",deleteRoles:"api/roles"}},ed=e=>Ot.post({url:Lt.user.signIn,data:e}),td=()=>Ot.get({url:Lt.user.userProfile}),rd=async(e=1,t)=>{try{const r={page:e,name:t};return await Ot.get({url:`${Lt.user.userList}`,params:r})}catch(r){throw console.error("Product API error:",r),r}},nd=async e=>{try{return await Ot.delete({url:`${Lt.user.deleteUsers}/${e}`})}catch(t){throw console.error("Product API error:",t),t}},od=async({id:e,user:t})=>{try{return await Ot.put({url:`${Lt.user.updateUsers}/${e}`,data:t})}catch(r){throw console.error("Product API error:",r),r}},ad=async e=>{try{return await Ot.post({url:`${Lt.user.createUser}`,data:e})}catch(t){throw console.error("Product API error:",t),t}};function sd(e){if(typeof document>"u")return;let t=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}const id=e=>{switch(e){case"success":return cd;case"info":return fd;case"warning":return dd;case"error":return hd;default:return null}},ld=Array(12).fill(0),ud=({visible:e,className:t})=>P.createElement("div",{className:["sonner-loading-wrapper",t].filter(Boolean).join(" "),"data-visible":e},P.createElement("div",{className:"sonner-spinner"},ld.map((r,n)=>P.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),cd=P.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},P.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),dd=P.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},P.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),fd=P.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},P.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),hd=P.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},P.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),pd=P.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},P.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),P.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),md=()=>{const[e,t]=P.useState(document.hidden);return P.useEffect(()=>{const r=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",r),()=>window.removeEventListener("visibilitychange",r)},[]),e};let Ln=1;class gd{constructor(){this.subscribe=t=>(this.subscribers.push(t),()=>{const r=this.subscribers.indexOf(t);this.subscribers.splice(r,1)}),this.publish=t=>{this.subscribers.forEach(r=>r(t))},this.addToast=t=>{this.publish(t),this.toasts=[...this.toasts,t]},this.create=t=>{var r;const{message:n,...o}=t,a=typeof t?.id=="number"||((r=t.id)==null?void 0:r.length)>0?t.id:Ln++,s=this.toasts.find(i=>i.id===a),l=t.dismissible===void 0?!0:t.dismissible;return this.dismissedToasts.has(a)&&this.dismissedToasts.delete(a),s?this.toasts=this.toasts.map(i=>i.id===a?(this.publish({...i,...t,id:a,title:n}),{...i,...t,id:a,dismissible:l,title:n}):i):this.addToast({title:n,...o,dismissible:l,id:a}),a},this.dismiss=t=>(t?(this.dismissedToasts.add(t),requestAnimationFrame(()=>this.subscribers.forEach(r=>r({id:t,dismiss:!0})))):this.toasts.forEach(r=>{this.subscribers.forEach(n=>n({id:r.id,dismiss:!0}))}),t),this.message=(t,r)=>this.create({...r,message:t}),this.error=(t,r)=>this.create({...r,message:t,type:"error"}),this.success=(t,r)=>this.create({...r,type:"success",message:t}),this.info=(t,r)=>this.create({...r,type:"info",message:t}),this.warning=(t,r)=>this.create({...r,type:"warning",message:t}),this.loading=(t,r)=>this.create({...r,type:"loading",message:t}),this.promise=(t,r)=>{if(!r)return;let n;r.loading!==void 0&&(n=this.create({...r,promise:t,type:"loading",message:r.loading,description:typeof r.description!="function"?r.description:void 0}));const o=Promise.resolve(t instanceof Function?t():t);let a=n!==void 0,s;const l=o.then(async u=>{if(s=["resolve",u],P.isValidElement(u))a=!1,this.create({id:n,type:"default",message:u});else if(vd(u)&&!u.ok){a=!1;const c=typeof r.error=="function"?await r.error(`HTTP error! status: ${u.status}`):r.error,h=typeof r.description=="function"?await r.description(`HTTP error! status: ${u.status}`):r.description,v=typeof c=="object"&&!P.isValidElement(c)?c:{message:c};this.create({id:n,type:"error",description:h,...v})}else if(u instanceof Error){a=!1;const c=typeof r.error=="function"?await r.error(u):r.error,h=typeof r.description=="function"?await r.description(u):r.description,v=typeof c=="object"&&!P.isValidElement(c)?c:{message:c};this.create({id:n,type:"error",description:h,...v})}else if(r.success!==void 0){a=!1;const c=typeof r.success=="function"?await r.success(u):r.success,h=typeof r.description=="function"?await r.description(u):r.description,v=typeof c=="object"&&!P.isValidElement(c)?c:{message:c};this.create({id:n,type:"success",description:h,...v})}}).catch(async u=>{if(s=["reject",u],r.error!==void 0){a=!1;const d=typeof r.error=="function"?await r.error(u):r.error,c=typeof r.description=="function"?await r.description(u):r.description,b=typeof d=="object"&&!P.isValidElement(d)?d:{message:d};this.create({id:n,type:"error",description:c,...b})}}).finally(()=>{a&&(this.dismiss(n),n=void 0),r.finally==null||r.finally.call(r)}),i=()=>new Promise((u,d)=>l.then(()=>s[0]==="reject"?d(s[1]):u(s[1])).catch(d));return typeof n!="string"&&typeof n!="number"?{unwrap:i}:Object.assign(n,{unwrap:i})},this.custom=(t,r)=>{const n=r?.id||Ln++;return this.create({jsx:t(n),id:n,...r}),n},this.getActiveToasts=()=>this.toasts.filter(t=>!this.dismissedToasts.has(t.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}}const _e=new gd,yd=(e,t)=>{const r=t?.id||Ln++;return _e.addToast({title:e,...t,id:r}),r},vd=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",bd=yd,wd=()=>_e.toasts,Ed=()=>_e.getActiveToasts(),wt=Object.assign(bd,{success:_e.success,info:_e.info,warning:_e.warning,error:_e.error,custom:_e.custom,message:_e.message,promise:_e.promise,dismiss:_e.dismiss,loading:_e.loading},{getHistory:wd,getToasts:Ed});sd("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");function wr(e){return e.label!==void 0}const xd=3,Rd="24px",Sd="16px",Yo=4e3,_d=356,Td=14,Pd=45,Cd=200;function We(...e){return e.filter(Boolean).join(" ")}function Od(e){const[t,r]=e.split("-"),n=[];return t&&n.push(t),r&&n.push(r),n}const Ld=e=>{var t,r,n,o,a,s,l,i,u;const{invert:d,toast:c,unstyled:h,interacting:b,setHeights:v,visibleToasts:R,heights:m,index:w,toasts:C,expanded:S,removeToast:A,defaultRichColors:D,closeButton:y,style:k,cancelButtonStyle:j,actionButtonStyle:B,className:K="",descriptionClassName:te="",duration:Se,position:X,gap:ee,expandByDefault:ae,classNames:I,icons:ne,closeButtonAriaLabel:oe="Close toast"}=e,[se,me]=P.useState(null),[q,Q]=P.useState(null),[J,ge]=P.useState(!1),[De,G]=P.useState(!1),[it,Ge]=P.useState(!1),[lt,le]=P.useState(!1),[Ze,Dt]=P.useState(!1),[Zr,Ne]=P.useState(0),[en,sr]=P.useState(0),et=P.useRef(c.duration||Se||Yo),ir=P.useRef(null),Fe=P.useRef(null),tn=w===0,rn=w+1<=R,fe=c.type,Ae=c.dismissible!==!1,lr=c.className||"",At=c.descriptionClassName||"",Oe=P.useMemo(()=>m.findIndex(z=>z.toastId===c.id)||0,[m,c.id]),Ue=P.useMemo(()=>{var z;return(z=c.closeButton)!=null?z:y},[c.closeButton,y]),It=P.useMemo(()=>c.duration||Se||Yo,[c.duration,Se]),ut=P.useRef(0),tt=P.useRef(0),je=P.useRef(0),Qe=P.useRef(null),[ur,cr]=X.split("-"),dr=P.useMemo(()=>m.reduce((z,ie,ve)=>ve>=Oe?z:z+ie.height,0),[m,Oe]),kt=md(),vt=c.invert||d,bt=fe==="loading";tt.current=P.useMemo(()=>Oe*ee+dr,[Oe,dr]),P.useEffect(()=>{et.current=It},[It]),P.useEffect(()=>{ge(!0)},[]),P.useEffect(()=>{const z=Fe.current;if(z){const ie=z.getBoundingClientRect().height;return sr(ie),v(ve=>[{toastId:c.id,height:ie,position:c.position},...ve]),()=>v(ve=>ve.filter(xe=>xe.toastId!==c.id))}},[v,c.id]),P.useLayoutEffect(()=>{if(!J)return;const z=Fe.current,ie=z.style.height;z.style.height="auto";const ve=z.getBoundingClientRect().height;z.style.height=ie,sr(ve),v(xe=>xe.find(p=>p.toastId===c.id)?xe.map(p=>p.toastId===c.id?{...p,height:ve}:p):[{toastId:c.id,height:ve,position:c.position},...xe])},[J,c.title,c.description,v,c.id,c.jsx,c.action,c.cancel]);const Ie=P.useCallback(()=>{G(!0),Ne(tt.current),v(z=>z.filter(ie=>ie.toastId!==c.id)),setTimeout(()=>{A(c)},Cd)},[c,A,v,tt]);P.useEffect(()=>{if(c.promise&&fe==="loading"||c.duration===1/0||c.type==="loading")return;let z;return S||b||kt?(()=>{if(je.current<ut.current){const xe=new Date().getTime()-ut.current;et.current=et.current-xe}je.current=new Date().getTime()})():(()=>{et.current!==1/0&&(ut.current=new Date().getTime(),z=setTimeout(()=>{c.onAutoClose==null||c.onAutoClose.call(c,c),Ie()},et.current))})(),()=>clearTimeout(z)},[S,b,c,fe,kt,Ie]),P.useEffect(()=>{c.delete&&(Ie(),c.onDismiss==null||c.onDismiss.call(c,c))},[Ie,c.delete]);function nn(){var z;if(ne?.loading){var ie;return P.createElement("div",{className:We(I?.loader,c==null||(ie=c.classNames)==null?void 0:ie.loader,"sonner-loader"),"data-visible":fe==="loading"},ne.loading)}return P.createElement(ud,{className:We(I?.loader,c==null||(z=c.classNames)==null?void 0:z.loader),visible:fe==="loading"})}const fr=c.icon||ne?.[fe]||id(fe);var hr,Mt;return P.createElement("li",{tabIndex:0,ref:Fe,className:We(K,lr,I?.toast,c==null||(t=c.classNames)==null?void 0:t.toast,I?.default,I?.[fe],c==null||(r=c.classNames)==null?void 0:r[fe]),"data-sonner-toast":"","data-rich-colors":(hr=c.richColors)!=null?hr:D,"data-styled":!(c.jsx||c.unstyled||h),"data-mounted":J,"data-promise":!!c.promise,"data-swiped":Ze,"data-removed":De,"data-visible":rn,"data-y-position":ur,"data-x-position":cr,"data-index":w,"data-front":tn,"data-swiping":it,"data-dismissible":Ae,"data-type":fe,"data-invert":vt,"data-swipe-out":lt,"data-swipe-direction":q,"data-expanded":!!(S||ae&&J),style:{"--index":w,"--toasts-before":w,"--z-index":C.length-w,"--offset":`${De?Zr:tt.current}px`,"--initial-height":ae?"auto":`${en}px`,...k,...c.style},onDragEnd:()=>{Ge(!1),me(null),Qe.current=null},onPointerDown:z=>{z.button!==2&&(bt||!Ae||(ir.current=new Date,Ne(tt.current),z.target.setPointerCapture(z.pointerId),z.target.tagName!=="BUTTON"&&(Ge(!0),Qe.current={x:z.clientX,y:z.clientY})))},onPointerUp:()=>{var z,ie,ve;if(lt||!Ae)return;Qe.current=null;const xe=Number(((z=Fe.current)==null?void 0:z.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),f=Number(((ie=Fe.current)==null?void 0:ie.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),p=new Date().getTime()-((ve=ir.current)==null?void 0:ve.getTime()),E=se==="x"?xe:f,_=Math.abs(E)/p;if(Math.abs(E)>=Pd||_>.11){Ne(tt.current),c.onDismiss==null||c.onDismiss.call(c,c),Q(se==="x"?xe>0?"right":"left":f>0?"down":"up"),Ie(),le(!0);return}else{var T,O;(T=Fe.current)==null||T.style.setProperty("--swipe-amount-x","0px"),(O=Fe.current)==null||O.style.setProperty("--swipe-amount-y","0px")}Dt(!1),Ge(!1),me(null)},onPointerMove:z=>{var ie,ve,xe;if(!Qe.current||!Ae||((ie=window.getSelection())==null?void 0:ie.toString().length)>0)return;const p=z.clientY-Qe.current.y,E=z.clientX-Qe.current.x;var _;const T=(_=e.swipeDirections)!=null?_:Od(X);!se&&(Math.abs(E)>1||Math.abs(p)>1)&&me(Math.abs(E)>Math.abs(p)?"x":"y");let O={x:0,y:0};const M=L=>1/(1.5+Math.abs(L)/20);if(se==="y"){if(T.includes("top")||T.includes("bottom"))if(T.includes("top")&&p<0||T.includes("bottom")&&p>0)O.y=p;else{const L=p*M(p);O.y=Math.abs(L)<Math.abs(p)?L:p}}else if(se==="x"&&(T.includes("left")||T.includes("right")))if(T.includes("left")&&E<0||T.includes("right")&&E>0)O.x=E;else{const L=E*M(E);O.x=Math.abs(L)<Math.abs(E)?L:E}(Math.abs(O.x)>0||Math.abs(O.y)>0)&&Dt(!0),(ve=Fe.current)==null||ve.style.setProperty("--swipe-amount-x",`${O.x}px`),(xe=Fe.current)==null||xe.style.setProperty("--swipe-amount-y",`${O.y}px`)}},Ue&&!c.jsx&&fe!=="loading"?P.createElement("button",{"aria-label":oe,"data-disabled":bt,"data-close-button":!0,onClick:bt||!Ae?()=>{}:()=>{Ie(),c.onDismiss==null||c.onDismiss.call(c,c)},className:We(I?.closeButton,c==null||(n=c.classNames)==null?void 0:n.closeButton)},(Mt=ne?.close)!=null?Mt:pd):null,(fe||c.icon||c.promise)&&c.icon!==null&&(ne?.[fe]!==null||c.icon)?P.createElement("div",{"data-icon":"",className:We(I?.icon,c==null||(o=c.classNames)==null?void 0:o.icon)},c.promise||c.type==="loading"&&!c.icon?c.icon||nn():null,c.type!=="loading"?fr:null):null,P.createElement("div",{"data-content":"",className:We(I?.content,c==null||(a=c.classNames)==null?void 0:a.content)},P.createElement("div",{"data-title":"",className:We(I?.title,c==null||(s=c.classNames)==null?void 0:s.title)},c.jsx?c.jsx:typeof c.title=="function"?c.title():c.title),c.description?P.createElement("div",{"data-description":"",className:We(te,At,I?.description,c==null||(l=c.classNames)==null?void 0:l.description)},typeof c.description=="function"?c.description():c.description):null),P.isValidElement(c.cancel)?c.cancel:c.cancel&&wr(c.cancel)?P.createElement("button",{"data-button":!0,"data-cancel":!0,style:c.cancelButtonStyle||j,onClick:z=>{wr(c.cancel)&&Ae&&(c.cancel.onClick==null||c.cancel.onClick.call(c.cancel,z),Ie())},className:We(I?.cancelButton,c==null||(i=c.classNames)==null?void 0:i.cancelButton)},c.cancel.label):null,P.isValidElement(c.action)?c.action:c.action&&wr(c.action)?P.createElement("button",{"data-button":!0,"data-action":!0,style:c.actionButtonStyle||B,onClick:z=>{wr(c.action)&&(c.action.onClick==null||c.action.onClick.call(c.action,z),!z.defaultPrevented&&Ie())},className:We(I?.actionButton,c==null||(u=c.classNames)==null?void 0:u.actionButton)},c.action.label):null)};function Xo(){if(typeof window>"u"||typeof document>"u")return"ltr";const e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}function Dd(e,t){const r={};return[e,t].forEach((n,o)=>{const a=o===1,s=a?"--mobile-offset":"--offset",l=a?Sd:Rd;function i(u){["top","right","bottom","left"].forEach(d=>{r[`${s}-${d}`]=typeof u=="number"?`${u}px`:u})}typeof n=="number"||typeof n=="string"?i(n):typeof n=="object"?["top","right","bottom","left"].forEach(u=>{n[u]===void 0?r[`${s}-${u}`]=l:r[`${s}-${u}`]=typeof n[u]=="number"?`${n[u]}px`:n[u]}):i(l)}),r}const Jh=P.forwardRef(function(t,r){const{invert:n,position:o="bottom-right",hotkey:a=["altKey","KeyT"],expand:s,closeButton:l,className:i,offset:u,mobileOffset:d,theme:c="light",richColors:h,duration:b,style:v,visibleToasts:R=xd,toastOptions:m,dir:w=Xo(),gap:C=Td,icons:S,containerAriaLabel:A="Notifications"}=t,[D,y]=P.useState([]),k=P.useMemo(()=>Array.from(new Set([o].concat(D.filter(q=>q.position).map(q=>q.position)))),[D,o]),[j,B]=P.useState([]),[K,te]=P.useState(!1),[Se,X]=P.useState(!1),[ee,ae]=P.useState(c!=="system"?c:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),I=P.useRef(null),ne=a.join("+").replace(/Key/g,"").replace(/Digit/g,""),oe=P.useRef(null),se=P.useRef(!1),me=P.useCallback(q=>{y(Q=>{var J;return(J=Q.find(ge=>ge.id===q.id))!=null&&J.delete||_e.dismiss(q.id),Q.filter(({id:ge})=>ge!==q.id)})},[]);return P.useEffect(()=>_e.subscribe(q=>{if(q.dismiss){requestAnimationFrame(()=>{y(Q=>Q.map(J=>J.id===q.id?{...J,delete:!0}:J))});return}setTimeout(()=>{Gs.flushSync(()=>{y(Q=>{const J=Q.findIndex(ge=>ge.id===q.id);return J!==-1?[...Q.slice(0,J),{...Q[J],...q},...Q.slice(J+1)]:[q,...Q]})})})}),[D]),P.useEffect(()=>{if(c!=="system"){ae(c);return}if(c==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?ae("dark"):ae("light")),typeof window>"u")return;const q=window.matchMedia("(prefers-color-scheme: dark)");try{q.addEventListener("change",({matches:Q})=>{ae(Q?"dark":"light")})}catch{q.addListener(({matches:J})=>{try{ae(J?"dark":"light")}catch(ge){console.error(ge)}})}},[c]),P.useEffect(()=>{D.length<=1&&te(!1)},[D]),P.useEffect(()=>{const q=Q=>{var J;if(a.every(G=>Q[G]||Q.code===G)){var De;te(!0),(De=I.current)==null||De.focus()}Q.code==="Escape"&&(document.activeElement===I.current||(J=I.current)!=null&&J.contains(document.activeElement))&&te(!1)};return document.addEventListener("keydown",q),()=>document.removeEventListener("keydown",q)},[a]),P.useEffect(()=>{if(I.current)return()=>{oe.current&&(oe.current.focus({preventScroll:!0}),oe.current=null,se.current=!1)}},[I.current]),P.createElement("section",{ref:r,"aria-label":`${A} ${ne}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},k.map((q,Q)=>{var J;const[ge,De]=q.split("-");return D.length?P.createElement("ol",{key:q,dir:w==="auto"?Xo():w,tabIndex:-1,ref:I,className:i,"data-sonner-toaster":!0,"data-sonner-theme":ee,"data-y-position":ge,"data-x-position":De,style:{"--front-toast-height":`${((J=j[0])==null?void 0:J.height)||0}px`,"--width":`${_d}px`,"--gap":`${C}px`,...v,...Dd(u,d)},onBlur:G=>{se.current&&!G.currentTarget.contains(G.relatedTarget)&&(se.current=!1,oe.current&&(oe.current.focus({preventScroll:!0}),oe.current=null))},onFocus:G=>{G.target instanceof HTMLElement&&G.target.dataset.dismissible==="false"||se.current||(se.current=!0,oe.current=G.relatedTarget)},onMouseEnter:()=>te(!0),onMouseMove:()=>te(!0),onMouseLeave:()=>{Se||te(!1)},onDragEnd:()=>te(!1),onPointerDown:G=>{G.target instanceof HTMLElement&&G.target.dataset.dismissible==="false"||X(!0)},onPointerUp:()=>X(!1)},D.filter(G=>!G.position&&Q===0||G.position===q).map((G,it)=>{var Ge,lt;return P.createElement(Ld,{key:G.id,icons:S,index:it,toast:G,defaultRichColors:h,duration:(Ge=m?.duration)!=null?Ge:b,className:m?.className,descriptionClassName:m?.descriptionClassName,invert:n,visibleToasts:R,closeButton:(lt=m?.closeButton)!=null?lt:l,interacting:Se,position:q,style:m?.style,unstyled:m?.unstyled,classNames:m?.classNames,cancelButtonStyle:m?.cancelButtonStyle,actionButtonStyle:m?.actionButtonStyle,closeButtonAriaLabel:m?.closeButtonAriaLabel,removeToast:me,toasts:D.filter(le=>le.position==G.position),heights:j.filter(le=>le.position==G.position),setHeights:B,expandByDefault:s,gap:C,expanded:K,swipeDirections:t.swipeDirections})})):null}))}),bs=()=>{const e=xt(a=>a.userToken),t=xt(a=>a.setUserToken),r=Er({mutationFn:ed});return{sinIn:async a=>{try{const s=await r.mutateAsync(a);t(s.data)}catch(s){throw console.log("登录错误:",s),s}},userToken:e,clearUserToken:()=>t({token:"",refresh_token:e.refresh_token})}},Ad=()=>{const e=xt(a=>a.userProfile),t=xt(a=>a.setUserProfile),r=xt(a=>a.userProfile.userRole?.permission),n=wa({queryKey:["user-profile"],queryFn:async()=>{try{const a=await td();return t(a.data),a.data}catch(a){console.error("API Error:",a)}},retry:3,staleTime:10*1e3,placeholderData:a=>a}),o=()=>t({});return{data:n.data,isLoading:n.isLoading,userProfile:e,clearUserInfo:o,userPermission:r,refetch:n.refetch}},Id=()=>{const[e,t]=g.useState(1),[r,n]=g.useState(""),o=Fn(),a=wa({queryKey:["user-list",e,r],queryFn:async()=>{try{const c=await rd(e,r);return c&&c.code===200&&c.data?c.data:{users:[],currentPage:1,size:10,totalCount:0,totalPages:1}}catch(c){console.error("API Error:",c)}},retry:3,staleTime:10*1e3,placeholderData:c=>c}),s=Er({mutationFn:nd,onSuccess:()=>{o.invalidateQueries({queryKey:["user-list"]}),wt("删除用户成功",{description:`您在 ${new Date().toUTCString()} 删除了用户`,action:{label:"确认",onClick:()=>console.log("用户已经确认")}})},onError:c=>{wt(`删除用户失败 ${c.message}`)}}),l=Er({mutationFn:ad,onSuccess:()=>{o.invalidateQueries({queryKey:["user-list"]}),wt("创建用户成功",{description:`您在 ${new Date().toUTCString()} 创建了用户`,action:{label:"确认",onClick:()=>console.log("用户已经确认")}})},onError:c=>{wt(`创建用户失败 ${c.message}`)}}),i=Er({mutationFn:od,onSuccess:()=>{o.invalidateQueries({queryKey:["user-list"]}),wt("更新用户成功",{description:`您在 ${new Date().toUTCString()} 更新了用户`,action:{label:"确认",onClick:()=>console.log("用户已经确认")}})},onError:c=>{wt(`更新用户失败 ${c.message}`)}}),u={users:[],currentPage:1,size:10,totalCount:0,totalPages:1},d=a.data||u;return{userListData:d,isLoading:a.isLoading,setFilterName:n,filterName:r,error:a.error,pagination:{totalCount:Number(d.totalCount)||0,totalPages:Number(d.totalPages)||1,currentPage:Number(d.currentPage)||1,setPage:t,getPage:e},isFeching:a.isFetching,deleteUser:s,updateUser:i,createUser:l}},Qh=()=>{const{updateUser:e,deleteUser:t,createUser:r}=Id(),[n,o]=g.useState(!1),[a,s]=g.useState(!1),[l,i]=g.useState(void 0),[u,d]=g.useState({}),c=()=>{const S=Object.keys(u).filter(A=>u[A]);t.mutate(S),d({})},h=Object.keys(u).filter(S=>u[S]).length;return{handleCreateUser:S=>{r.mutate(S)},handleDeleteUser:S=>{t.mutate([S])},handleEditUser:S=>{e.mutate({id:S.id,user:S})},handleOpenEditDialog:S=>{i(S),s(!0)},handleCloseDialog:()=>{o(!1),s(!1),i(void 0)},handleOpenCreateDialog:()=>{o(!0)},handleDeleteSelected:c,setRowSelection:d,rowSelection:u,selectedCount:h,isCreateDialogOpen:n,isEditDialogOpen:a,editingItem:l}};function kd({children:e}){const{userToken:t}=bs(),r=Yr(),n=Xe();return g.useEffect(()=>{!t.token&&n.pathname!=="/login"&&r("/login",{replace:!0})},[t.token,r,n.pathname]),t.token?he.jsx(he.Fragment,{children:e}):null}function ws({mode:e,showAlways:t=!1}){const r=lc();return t||r.state==="loading"||e==="dev"?he.jsxs("div",{className:"flex justify-center items-center flex-col h-screen",children:[he.jsx("div",{className:Zs.loading}),he.jsx("p",{className:"mt-4 text-gray-600",children:"加载中..."})]}):null}function at(e){return e!=null&&typeof e=="object"&&e["@@functional/placeholder"]===!0}function Je(e){return function t(r){return arguments.length===0||at(r)?t:e.apply(this,arguments)}}function ar(e){return function t(r,n){switch(arguments.length){case 0:return t;case 1:return at(r)?t:Je(function(o){return e(r,o)});default:return at(r)&&at(n)?t:at(r)?Je(function(o){return e(o,n)}):at(n)?Je(function(o){return e(r,o)}):e(r,n)}}}function Xn(e,t){switch(e){case 0:return function(){return t.apply(this,arguments)};case 1:return function(r){return t.apply(this,arguments)};case 2:return function(r,n){return t.apply(this,arguments)};case 3:return function(r,n,o){return t.apply(this,arguments)};case 4:return function(r,n,o,a){return t.apply(this,arguments)};case 5:return function(r,n,o,a,s){return t.apply(this,arguments)};case 6:return function(r,n,o,a,s,l){return t.apply(this,arguments)};case 7:return function(r,n,o,a,s,l,i){return t.apply(this,arguments)};case 8:return function(r,n,o,a,s,l,i,u){return t.apply(this,arguments)};case 9:return function(r,n,o,a,s,l,i,u,d){return t.apply(this,arguments)};case 10:return function(r,n,o,a,s,l,i,u,d,c){return t.apply(this,arguments)};default:throw new Error("First argument to _arity must be a non-negative integer no greater than ten")}}function Es(e,t,r){return function(){for(var n=[],o=0,a=e,s=0,l=!1;s<t.length||o<arguments.length;){var i;s<t.length&&(!at(t[s])||o>=arguments.length)?i=t[s]:(i=arguments[o],o+=1),n[s]=i,at(i)?l=!0:a-=1,s+=1}return!l&&a<=0?r.apply(this,n):Xn(Math.max(0,a),Es(e,n,r))}}var Md=ar(function(t,r){return t===1?Je(r):Xn(t,Es(t,[],r))});const Gn=Array.isArray||function(t){return t!=null&&t.length>=0&&Object.prototype.toString.call(t)==="[object Array]"};function Nd(e){return e!=null&&typeof e["@@transducer/step"]=="function"}function xs(e,t,r){return function(){if(arguments.length===0)return r();var n=arguments[arguments.length-1];if(!Gn(n)){for(var o=0;o<e.length;){if(typeof n[e[o]]=="function")return n[e[o]].apply(n,Array.prototype.slice.call(arguments,0,-1));o+=1}if(Nd(n)){var a=t.apply(null,Array.prototype.slice.call(arguments,0,-1));return a(n)}}return r.apply(this,arguments)}}const St={init:function(){return this.xf["@@transducer/init"]()},result:function(e){return this.xf["@@transducer/result"](e)}};function Go(e){for(var t=[],r;!(r=e.next()).done;)t.push(r.value);return t}function Zo(e,t,r){for(var n=0,o=r.length;n<o;){if(e(t,r[n]))return!0;n+=1}return!1}function Fd(e){var t=String(e).match(/^function (\w*)/);return t==null?"":t[1]}function Ur(e,t){return Object.prototype.hasOwnProperty.call(t,e)}function Ud(e,t){return e===t?e!==0||1/e===1/t:e!==e&&t!==t}const pn=typeof Object.is=="function"?Object.is:Ud;var ea=Object.prototype.toString,Rs=function(){return ea.call(arguments)==="[object Arguments]"?function(t){return ea.call(t)==="[object Arguments]"}:function(t){return Ur("callee",t)}}(),jd=!{toString:null}.propertyIsEnumerable("toString"),ta=["constructor","valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"],ra=function(){return arguments.propertyIsEnumerable("length")}(),Bd=function(t,r){for(var n=0;n<t.length;){if(t[n]===r)return!0;n+=1}return!1},Dn=Je(typeof Object.keys=="function"&&!ra?function(t){return Object(t)!==t?[]:Object.keys(t)}:function(t){if(Object(t)!==t)return[];var r,n,o=[],a=ra&&Rs(t);for(r in t)Ur(r,t)&&(!a||r!=="length")&&(o[o.length]=r);if(jd)for(n=ta.length-1;n>=0;)r=ta[n],Ur(r,t)&&!Bd(o,r)&&(o[o.length]=r),n-=1;return o}),na=Je(function(t){return t===null?"Null":t===void 0?"Undefined":Object.prototype.toString.call(t).slice(8,-1)});function oa(e,t,r,n){var o=Go(e),a=Go(t);function s(l,i){return Zn(l,i,r.slice(),n.slice())}return!Zo(function(l,i){return!Zo(s,i,l)},a,o)}function Zn(e,t,r,n){if(pn(e,t))return!0;var o=na(e);if(o!==na(t))return!1;if(typeof e["fantasy-land/equals"]=="function"||typeof t["fantasy-land/equals"]=="function")return typeof e["fantasy-land/equals"]=="function"&&e["fantasy-land/equals"](t)&&typeof t["fantasy-land/equals"]=="function"&&t["fantasy-land/equals"](e);if(typeof e.equals=="function"||typeof t.equals=="function")return typeof e.equals=="function"&&e.equals(t)&&typeof t.equals=="function"&&t.equals(e);switch(o){case"Arguments":case"Array":case"Object":if(typeof e.constructor=="function"&&Fd(e.constructor)==="Promise")return e===t;break;case"Boolean":case"Number":case"String":if(!(typeof e==typeof t&&pn(e.valueOf(),t.valueOf())))return!1;break;case"Date":if(!pn(e.valueOf(),t.valueOf()))return!1;break;case"Error":return e.name===t.name&&e.message===t.message;case"RegExp":if(!(e.source===t.source&&e.global===t.global&&e.ignoreCase===t.ignoreCase&&e.multiline===t.multiline&&e.sticky===t.sticky&&e.unicode===t.unicode))return!1;break}for(var a=r.length-1;a>=0;){if(r[a]===e)return n[a]===t;a-=1}switch(o){case"Map":return e.size!==t.size?!1:oa(e.entries(),t.entries(),r.concat([e]),n.concat([t]));case"Set":return e.size!==t.size?!1:oa(e.values(),t.values(),r.concat([e]),n.concat([t]));case"Arguments":case"Array":case"Object":case"Boolean":case"Number":case"String":case"Date":case"Error":case"RegExp":case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"ArrayBuffer":break;default:return!1}var s=Dn(e);if(s.length!==Dn(t).length)return!1;var l=r.concat([e]),i=n.concat([t]);for(a=s.length-1;a>=0;){var u=s[a];if(!(Ur(u,t)&&Zn(t[u],e[u],l,i)))return!1;a-=1}return!0}var $d=ar(function(t,r){return Zn(t,r,[],[])});function zd(e,t){for(var r=0,n=t.length,o=Array(n);r<n;)o[r]=e(t[r]),r+=1;return o}function Hd(e,t,r){for(var n=0,o=r.length;n<o;)t=e(t,r[n]),n+=1;return t}function Vd(e){return Object.prototype.toString.call(e)==="[object Object]"}var qd=function(){function e(t,r){this.xf=r,this.f=t}return e.prototype["@@transducer/init"]=St.init,e.prototype["@@transducer/result"]=St.result,e.prototype["@@transducer/step"]=function(t,r){return this.xf["@@transducer/step"](t,this.f(r))},e}(),Ss=function(t){return function(r){return new qd(t,r)}},Wd=ar(xs(["fantasy-land/map","map"],Ss,function(t,r){switch(Object.prototype.toString.call(r)){case"[object Function]":return Md(r.length,function(){return t.call(this,r.apply(this,arguments))});case"[object Object]":return Hd(function(n,o){return n[o]=t(r[o]),n},{},Dn(r));default:return zd(t,r)}}));function _s(e){return Object.prototype.toString.call(e)==="[object String]"}var eo=Je(function(t){return Gn(t)?!0:!t||typeof t!="object"||_s(t)?!1:t.length===0?!0:t.length>0?t.hasOwnProperty(0)&&t.hasOwnProperty(t.length-1):!1}),aa=typeof Symbol<"u"?Symbol.iterator:"@@iterator";function Kd(e,t,r){return function(o,a,s){if(eo(s))return e(o,a,s);if(s==null)return a;if(typeof s["fantasy-land/reduce"]=="function")return t(o,a,s,"fantasy-land/reduce");if(s[aa]!=null)return r(o,a,s[aa]());if(typeof s.next=="function")return r(o,a,s);if(typeof s.reduce=="function")return t(o,a,s,"reduce");throw new TypeError("reduce: list must be array or iterable")}}function Ts(e,t,r){for(var n=0,o=r.length;n<o;){if(t=e["@@transducer/step"](t,r[n]),t&&t["@@transducer/reduced"]){t=t["@@transducer/value"];break}n+=1}return e["@@transducer/result"](t)}var Jd=ar(function(t,r){return Xn(t.length,function(){return t.apply(r,arguments)})});function Qd(e,t,r){for(var n=r.next();!n.done;){if(t=e["@@transducer/step"](t,n.value),t&&t["@@transducer/reduced"]){t=t["@@transducer/value"];break}n=r.next()}return e["@@transducer/result"](t)}function Yd(e,t,r,n){return e["@@transducer/result"](r[n](Jd(e["@@transducer/step"],e),t))}var Xd=Kd(Ts,Yd,Qd);function Gd(e){return function(r){for(var n,o,a,s=[],l=0,i=r.length;l<i;){if(eo(r[l]))for(n=r[l],a=0,o=n.length;a<o;)s[s.length]=n[a],a+=1;else s[s.length]=r[l];l+=1}return s}}function Zd(e){return{"@@transducer/value":e,"@@transducer/reduced":!0}}var Ps="@@transducer/init",An="@@transducer/step",Cs="@@transducer/result",ef=function(){function e(t){this.xf=t}return e.prototype[Ps]=St.init,e.prototype[Cs]=St.result,e.prototype[An]=function(t,r){var n=this.xf[An](t,r);return n["@@transducer/reduced"]?Zd(n):n},e}(),tf=function(){function e(t){this.xf=new ef(t)}return e.prototype[Ps]=St.init,e.prototype[Cs]=St.result,e.prototype[An]=function(t,r){return eo(r)?Xd(this.xf,t,r):Ts(this.xf,t,[r])},e}(),rf=function(t){return new tf(t)};function nf(e){return function(t){return Ss(e)(rf(t))}}const of=xs(["fantasy-land/chain","chain"],nf,function(t,r){return typeof r=="function"?function(n){return t(r(n))(n)}:Gd()(Wd(t,r))});var af=ar(of);function sf(e){var t=Object.prototype.toString.call(e);return t==="[object Uint8ClampedArray]"||t==="[object Int8Array]"||t==="[object Uint8Array]"||t==="[object Int16Array]"||t==="[object Uint16Array]"||t==="[object Int32Array]"||t==="[object Uint32Array]"||t==="[object Float32Array]"||t==="[object Float64Array]"||t==="[object BigInt64Array]"||t==="[object BigUint64Array]"}var lf=Je(function(t){return t!=null&&typeof t["fantasy-land/empty"]=="function"?t["fantasy-land/empty"]():t!=null&&t.constructor!=null&&typeof t.constructor["fantasy-land/empty"]=="function"?t.constructor["fantasy-land/empty"]():t!=null&&typeof t.empty=="function"?t.empty():t!=null&&t.constructor!=null&&typeof t.constructor.empty=="function"?t.constructor.empty():t==Set||t instanceof Set?new Set:t==Map||t instanceof Map?new Map:Gn(t)?[]:_s(t)?"":Vd(t)?{}:Rs(t)?function(){return arguments}():sf(t)?t.constructor.from(""):void 0}),uf=Je(function(t){return t!=null&&$d(t,lf(t))});function Os(e=[]){return af(t=>{const r=t.children||[];return[t,...Os(r)]},e)}var Ls=(e=>(e[e.DISABLE=0]="DISABLE",e[e.ENABLE=1]="ENABLE",e))(Ls||{}),Ds=(e=>(e[e.CATALOGUE=0]="CATALOGUE",e[e.MENU=1]="MENU",e[e.BUTTON=2]="BUTTON",e))(Ds||{});const As=Object.freeze({left:0,top:0,width:16,height:16}),jr=Object.freeze({rotate:0,vFlip:!1,hFlip:!1}),to=Object.freeze({...As,...jr}),In=Object.freeze({...to,body:"",hidden:!1});function cf(e,t){const r={};!e.hFlip!=!t.hFlip&&(r.hFlip=!0),!e.vFlip!=!t.vFlip&&(r.vFlip=!0);const n=((e.rotate||0)+(t.rotate||0))%4;return n&&(r.rotate=n),r}function sa(e,t){const r=cf(e,t);for(const n in In)n in jr?n in e&&!(n in r)&&(r[n]=jr[n]):n in t?r[n]=t[n]:n in e&&(r[n]=e[n]);return r}function df(e,t){const r=e.icons,n=e.aliases||Object.create(null),o=Object.create(null);function a(s){if(r[s])return o[s]=[];if(!(s in o)){o[s]=null;const l=n[s]&&n[s].parent,i=l&&a(l);i&&(o[s]=[l].concat(i))}return o[s]}return Object.keys(r).concat(Object.keys(n)).forEach(a),o}function ff(e,t,r){const n=e.icons,o=e.aliases||Object.create(null);let a={};function s(l){a=sa(n[l]||o[l],a)}return s(t),r.forEach(s),sa(e,a)}function Is(e,t){const r=[];if(typeof e!="object"||typeof e.icons!="object")return r;e.not_found instanceof Array&&e.not_found.forEach(o=>{t(o,null),r.push(o)});const n=df(e);for(const o in n){const a=n[o];a&&(t(o,ff(e,o,a)),r.push(o))}return r}const hf={provider:"",aliases:{},not_found:{},...As};function mn(e,t){for(const r in t)if(r in e&&typeof e[r]!=typeof t[r])return!1;return!0}function ks(e){if(typeof e!="object"||e===null)return null;const t=e;if(typeof t.prefix!="string"||!e.icons||typeof e.icons!="object"||!mn(e,hf))return null;const r=t.icons;for(const o in r){const a=r[o];if(!o||typeof a.body!="string"||!mn(a,In))return null}const n=t.aliases||Object.create(null);for(const o in n){const a=n[o],s=a.parent;if(!o||typeof s!="string"||!r[s]&&!n[s]||!mn(a,In))return null}return t}const Ms=/^[a-z0-9]+(-[a-z0-9]+)*$/,Gr=(e,t,r,n="")=>{const o=e.split(":");if(e.slice(0,1)==="@"){if(o.length<2||o.length>3)return null;n=o.shift().slice(1)}if(o.length>3||!o.length)return null;if(o.length>1){const l=o.pop(),i=o.pop(),u={provider:o.length>0?o[0]:n,prefix:i,name:l};return t&&!Or(u)?null:u}const a=o[0],s=a.split("-");if(s.length>1){const l={provider:n,prefix:s.shift(),name:s.join("-")};return t&&!Or(l)?null:l}if(r&&n===""){const l={provider:n,prefix:"",name:a};return t&&!Or(l,r)?null:l}return null},Or=(e,t)=>e?!!((t&&e.prefix===""||e.prefix)&&e.name):!1,ia=Object.create(null);function pf(e,t){return{provider:e,prefix:t,icons:Object.create(null),missing:new Set}}function _t(e,t){const r=ia[e]||(ia[e]=Object.create(null));return r[t]||(r[t]=pf(e,t))}function Ns(e,t){return ks(t)?Is(t,(r,n)=>{n?e.icons[r]=n:e.missing.add(r)}):[]}function mf(e,t,r){try{if(typeof r.body=="string")return e.icons[t]={...r},!0}catch{}return!1}let Gt=!1;function Fs(e){return typeof e=="boolean"&&(Gt=e),Gt}function la(e){const t=typeof e=="string"?Gr(e,!0,Gt):e;if(t){const r=_t(t.provider,t.prefix),n=t.name;return r.icons[n]||(r.missing.has(n)?null:void 0)}}function gf(e,t){const r=Gr(e,!0,Gt);if(!r)return!1;const n=_t(r.provider,r.prefix);return t?mf(n,r.name,t):(n.missing.add(r.name),!0)}function yf(e,t){if(typeof e!="object")return!1;if(typeof t!="string"&&(t=e.provider||""),Gt&&!t&&!e.prefix){let o=!1;return ks(e)&&(e.prefix="",Is(e,(a,s)=>{gf(a,s)&&(o=!0)})),o}const r=e.prefix;if(!Or({prefix:r,name:"a"}))return!1;const n=_t(t,r);return!!Ns(n,e)}const Us=Object.freeze({width:null,height:null}),js=Object.freeze({...Us,...jr}),vf=/(-?[0-9.]*[0-9]+[0-9.]*)/g,bf=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function ua(e,t,r){if(t===1)return e;if(r=r||100,typeof e=="number")return Math.ceil(e*t*r)/r;if(typeof e!="string")return e;const n=e.split(vf);if(n===null||!n.length)return e;const o=[];let a=n.shift(),s=bf.test(a);for(;;){if(s){const l=parseFloat(a);isNaN(l)?o.push(a):o.push(Math.ceil(l*t*r)/r)}else o.push(a);if(a=n.shift(),a===void 0)return o.join("");s=!s}}function wf(e,t="defs"){let r="";const n=e.indexOf("<"+t);for(;n>=0;){const o=e.indexOf(">",n),a=e.indexOf("</"+t);if(o===-1||a===-1)break;const s=e.indexOf(">",a);if(s===-1)break;r+=e.slice(o+1,a).trim(),e=e.slice(0,n).trim()+e.slice(s+1)}return{defs:r,content:e}}function Ef(e,t){return e?"<defs>"+e+"</defs>"+t:t}function xf(e,t,r){const n=wf(e);return Ef(n.defs,t+n.content+r)}const Rf=e=>e==="unset"||e==="undefined"||e==="none";function Sf(e,t){const r={...to,...e},n={...js,...t},o={left:r.left,top:r.top,width:r.width,height:r.height};let a=r.body;[r,n].forEach(R=>{const m=[],w=R.hFlip,C=R.vFlip;let S=R.rotate;w?C?S+=2:(m.push("translate("+(o.width+o.left).toString()+" "+(0-o.top).toString()+")"),m.push("scale(-1 1)"),o.top=o.left=0):C&&(m.push("translate("+(0-o.left).toString()+" "+(o.height+o.top).toString()+")"),m.push("scale(1 -1)"),o.top=o.left=0);let A;switch(S<0&&(S-=Math.floor(S/4)*4),S=S%4,S){case 1:A=o.height/2+o.top,m.unshift("rotate(90 "+A.toString()+" "+A.toString()+")");break;case 2:m.unshift("rotate(180 "+(o.width/2+o.left).toString()+" "+(o.height/2+o.top).toString()+")");break;case 3:A=o.width/2+o.left,m.unshift("rotate(-90 "+A.toString()+" "+A.toString()+")");break}S%2===1&&(o.left!==o.top&&(A=o.left,o.left=o.top,o.top=A),o.width!==o.height&&(A=o.width,o.width=o.height,o.height=A)),m.length&&(a=xf(a,'<g transform="'+m.join(" ")+'">',"</g>"))});const s=n.width,l=n.height,i=o.width,u=o.height;let d,c;s===null?(c=l===null?"1em":l==="auto"?u:l,d=ua(c,i/u)):(d=s==="auto"?i:s,c=l===null?ua(d,u/i):l==="auto"?u:l);const h={},b=(R,m)=>{Rf(m)||(h[R]=m.toString())};b("width",d),b("height",c);const v=[o.left,o.top,i,u];return h.viewBox=v.join(" "),{attributes:h,viewBox:v,body:a}}const _f=/\sid="(\S+)"/g,Tf="IconifyId"+Date.now().toString(16)+(Math.random()*16777216|0).toString(16);let Pf=0;function Cf(e,t=Tf){const r=[];let n;for(;n=_f.exec(e);)r.push(n[1]);if(!r.length)return e;const o="suffix"+(Math.random()*16777216|Date.now()).toString(16);return r.forEach(a=>{const s=typeof t=="function"?t(a):t+(Pf++).toString(),l=a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");e=e.replace(new RegExp('([#;"])('+l+')([")]|\\.[a-z])',"g"),"$1"+s+o+"$3")}),e=e.replace(new RegExp(o,"g"),""),e}const kn=Object.create(null);function Of(e,t){kn[e]=t}function Mn(e){return kn[e]||kn[""]}function ro(e){let t;if(typeof e.resources=="string")t=[e.resources];else if(t=e.resources,!(t instanceof Array)||!t.length)return null;return{resources:t,path:e.path||"/",maxURL:e.maxURL||500,rotate:e.rotate||750,timeout:e.timeout||5e3,random:e.random===!0,index:e.index||0,dataAfterTimeout:e.dataAfterTimeout!==!1}}const no=Object.create(null),Ht=["https://api.simplesvg.com","https://api.unisvg.com"],Lr=[];for(;Ht.length>0;)Ht.length===1||Math.random()>.5?Lr.push(Ht.shift()):Lr.push(Ht.pop());no[""]=ro({resources:["https://api.iconify.design"].concat(Lr)});function Lf(e,t){const r=ro(t);return r===null?!1:(no[e]=r,!0)}function oo(e){return no[e]}const Df=()=>{let e;try{if(e=fetch,typeof e=="function")return e}catch{}};let ca=Df();function Af(e,t){const r=oo(e);if(!r)return 0;let n;if(!r.maxURL)n=0;else{let o=0;r.resources.forEach(s=>{o=Math.max(o,s.length)});const a=t+".json?icons=";n=r.maxURL-o-r.path.length-a.length}return n}function If(e){return e===404}const kf=(e,t,r)=>{const n=[],o=Af(e,t),a="icons";let s={type:a,provider:e,prefix:t,icons:[]},l=0;return r.forEach((i,u)=>{l+=i.length+1,l>=o&&u>0&&(n.push(s),s={type:a,provider:e,prefix:t,icons:[]},l=i.length),s.icons.push(i)}),n.push(s),n};function Mf(e){if(typeof e=="string"){const t=oo(e);if(t)return t.path}return"/"}const Nf=(e,t,r)=>{if(!ca){r("abort",424);return}let n=Mf(t.provider);switch(t.type){case"icons":{const a=t.prefix,l=t.icons.join(","),i=new URLSearchParams({icons:l});n+=a+".json?"+i.toString();break}case"custom":{const a=t.uri;n+=a.slice(0,1)==="/"?a.slice(1):a;break}default:r("abort",400);return}let o=503;ca(e+n).then(a=>{const s=a.status;if(s!==200){setTimeout(()=>{r(If(s)?"abort":"next",s)});return}return o=501,a.json()}).then(a=>{if(typeof a!="object"||a===null){setTimeout(()=>{a===404?r("abort",a):r("next",o)});return}setTimeout(()=>{r("success",a)})}).catch(()=>{r("next",o)})},Ff={prepare:kf,send:Nf};function Uf(e){const t={loaded:[],missing:[],pending:[]},r=Object.create(null);e.sort((o,a)=>o.provider!==a.provider?o.provider.localeCompare(a.provider):o.prefix!==a.prefix?o.prefix.localeCompare(a.prefix):o.name.localeCompare(a.name));let n={provider:"",prefix:"",name:""};return e.forEach(o=>{if(n.name===o.name&&n.prefix===o.prefix&&n.provider===o.provider)return;n=o;const a=o.provider,s=o.prefix,l=o.name,i=r[a]||(r[a]=Object.create(null)),u=i[s]||(i[s]=_t(a,s));let d;l in u.icons?d=t.loaded:s===""||u.missing.has(l)?d=t.missing:d=t.pending;const c={provider:a,prefix:s,name:l};d.push(c)}),t}function Bs(e,t){e.forEach(r=>{const n=r.loaderCallbacks;n&&(r.loaderCallbacks=n.filter(o=>o.id!==t))})}function jf(e){e.pendingCallbacksFlag||(e.pendingCallbacksFlag=!0,setTimeout(()=>{e.pendingCallbacksFlag=!1;const t=e.loaderCallbacks?e.loaderCallbacks.slice(0):[];if(!t.length)return;let r=!1;const n=e.provider,o=e.prefix;t.forEach(a=>{const s=a.icons,l=s.pending.length;s.pending=s.pending.filter(i=>{if(i.prefix!==o)return!0;const u=i.name;if(e.icons[u])s.loaded.push({provider:n,prefix:o,name:u});else if(e.missing.has(u))s.missing.push({provider:n,prefix:o,name:u});else return r=!0,!0;return!1}),s.pending.length!==l&&(r||Bs([e],a.id),a.callback(s.loaded.slice(0),s.missing.slice(0),s.pending.slice(0),a.abort))})}))}let Bf=0;function $f(e,t,r){const n=Bf++,o=Bs.bind(null,r,n);if(!t.pending.length)return o;const a={id:n,icons:t,callback:e,abort:o};return r.forEach(s=>{(s.loaderCallbacks||(s.loaderCallbacks=[])).push(a)}),o}function zf(e,t=!0,r=!1){const n=[];return e.forEach(o=>{const a=typeof o=="string"?Gr(o,t,r):o;a&&n.push(a)}),n}var Hf={resources:[],index:0,timeout:2e3,rotate:750,random:!1,dataAfterTimeout:!1};function Vf(e,t,r,n){const o=e.resources.length,a=e.random?Math.floor(Math.random()*o):e.index;let s;if(e.random){let y=e.resources.slice(0);for(s=[];y.length>1;){const k=Math.floor(Math.random()*y.length);s.push(y[k]),y=y.slice(0,k).concat(y.slice(k+1))}s=s.concat(y)}else s=e.resources.slice(a).concat(e.resources.slice(0,a));const l=Date.now();let i="pending",u=0,d,c=null,h=[],b=[];typeof n=="function"&&b.push(n);function v(){c&&(clearTimeout(c),c=null)}function R(){i==="pending"&&(i="aborted"),v(),h.forEach(y=>{y.status==="pending"&&(y.status="aborted")}),h=[]}function m(y,k){k&&(b=[]),typeof y=="function"&&b.push(y)}function w(){return{startTime:l,payload:t,status:i,queriesSent:u,queriesPending:h.length,subscribe:m,abort:R}}function C(){i="failed",b.forEach(y=>{y(void 0,d)})}function S(){h.forEach(y=>{y.status==="pending"&&(y.status="aborted")}),h=[]}function A(y,k,j){const B=k!=="success";switch(h=h.filter(K=>K!==y),i){case"pending":break;case"failed":if(B||!e.dataAfterTimeout)return;break;default:return}if(k==="abort"){d=j,C();return}if(B){d=j,h.length||(s.length?D():C());return}if(v(),S(),!e.random){const K=e.resources.indexOf(y.resource);K!==-1&&K!==e.index&&(e.index=K)}i="completed",b.forEach(K=>{K(j)})}function D(){if(i!=="pending")return;v();const y=s.shift();if(y===void 0){if(h.length){c=setTimeout(()=>{v(),i==="pending"&&(S(),C())},e.timeout);return}C();return}const k={status:"pending",resource:y,callback:(j,B)=>{A(k,j,B)}};h.push(k),u++,c=setTimeout(D,e.rotate),r(y,t,k.callback)}return setTimeout(D),w}function $s(e){const t={...Hf,...e};let r=[];function n(){r=r.filter(l=>l().status==="pending")}function o(l,i,u){const d=Vf(t,l,i,(c,h)=>{n(),u&&u(c,h)});return r.push(d),d}function a(l){return r.find(i=>l(i))||null}return{query:o,find:a,setIndex:l=>{t.index=l},getIndex:()=>t.index,cleanup:n}}function da(){}const gn=Object.create(null);function qf(e){if(!gn[e]){const t=oo(e);if(!t)return;const r=$s(t),n={config:t,redundancy:r};gn[e]=n}return gn[e]}function Wf(e,t,r){let n,o;if(typeof e=="string"){const a=Mn(e);if(!a)return r(void 0,424),da;o=a.send;const s=qf(e);s&&(n=s.redundancy)}else{const a=ro(e);if(a){n=$s(a);const s=e.resources?e.resources[0]:"",l=Mn(s);l&&(o=l.send)}}return!n||!o?(r(void 0,424),da):n.query(t,o,r)().abort}function fa(){}function Kf(e){e.iconsLoaderFlag||(e.iconsLoaderFlag=!0,setTimeout(()=>{e.iconsLoaderFlag=!1,jf(e)}))}function Jf(e){const t=[],r=[];return e.forEach(n=>{(n.match(Ms)?t:r).push(n)}),{valid:t,invalid:r}}function Vt(e,t,r){function n(){const o=e.pendingIcons;t.forEach(a=>{o&&o.delete(a),e.icons[a]||e.missing.add(a)})}if(r&&typeof r=="object")try{if(!Ns(e,r).length){n();return}}catch(o){console.error(o)}n(),Kf(e)}function ha(e,t){e instanceof Promise?e.then(r=>{t(r)}).catch(()=>{t(null)}):t(e)}function Qf(e,t){e.iconsToLoad?e.iconsToLoad=e.iconsToLoad.concat(t).sort():e.iconsToLoad=t,e.iconsQueueFlag||(e.iconsQueueFlag=!0,setTimeout(()=>{e.iconsQueueFlag=!1;const{provider:r,prefix:n}=e,o=e.iconsToLoad;if(delete e.iconsToLoad,!o||!o.length)return;const a=e.loadIcon;if(e.loadIcons&&(o.length>1||!a)){ha(e.loadIcons(o,n,r),d=>{Vt(e,o,d)});return}if(a){o.forEach(d=>{const c=a(d,n,r);ha(c,h=>{const b=h?{prefix:n,icons:{[d]:h}}:null;Vt(e,[d],b)})});return}const{valid:s,invalid:l}=Jf(o);if(l.length&&Vt(e,l,null),!s.length)return;const i=n.match(Ms)?Mn(r):null;if(!i){Vt(e,s,null);return}i.prepare(r,n,s).forEach(d=>{Wf(r,d,c=>{Vt(e,d.icons,c)})})}))}const Yf=(e,t)=>{const r=zf(e,!0,Fs()),n=Uf(r);if(!n.pending.length){let i=!0;return t&&setTimeout(()=>{i&&t(n.loaded,n.missing,n.pending,fa)}),()=>{i=!1}}const o=Object.create(null),a=[];let s,l;return n.pending.forEach(i=>{const{provider:u,prefix:d}=i;if(d===l&&u===s)return;s=u,l=d,a.push(_t(u,d));const c=o[u]||(o[u]=Object.create(null));c[d]||(c[d]=[])}),n.pending.forEach(i=>{const{provider:u,prefix:d,name:c}=i,h=_t(u,d),b=h.pendingIcons||(h.pendingIcons=new Set);b.has(c)||(b.add(c),o[u][d].push(c))}),a.forEach(i=>{const u=o[i.provider][i.prefix];u.length&&Qf(i,u)}),t?$f(t,n,a):fa};function Xf(e,t){const r={...e};for(const n in t){const o=t[n],a=typeof o;n in Us?(o===null||o&&(a==="string"||a==="number"))&&(r[n]=o):a===typeof r[n]&&(r[n]=n==="rotate"?o%4:o)}return r}const Gf=/[\s,]+/;function Zf(e,t){t.split(Gf).forEach(r=>{switch(r.trim()){case"horizontal":e.hFlip=!0;break;case"vertical":e.vFlip=!0;break}})}function eh(e,t=0){const r=e.replace(/^-?[0-9.]*/,"");function n(o){for(;o<0;)o+=4;return o%4}if(r===""){const o=parseInt(e);return isNaN(o)?0:n(o)}else if(r!==e){let o=0;switch(r){case"%":o=25;break;case"deg":o=90}if(o){let a=parseFloat(e.slice(0,e.length-r.length));return isNaN(a)?0:(a=a/o,a%1===0?n(a):0)}}return t}function th(e,t){let r=e.indexOf("xlink:")===-1?"":' xmlns:xlink="http://www.w3.org/1999/xlink"';for(const n in t)r+=" "+n+'="'+t[n]+'"';return'<svg xmlns="http://www.w3.org/2000/svg"'+r+">"+e+"</svg>"}function rh(e){return e.replace(/"/g,"'").replace(/%/g,"%25").replace(/#/g,"%23").replace(/</g,"%3C").replace(/>/g,"%3E").replace(/\s+/g," ")}function nh(e){return"data:image/svg+xml,"+rh(e)}function oh(e){return'url("'+nh(e)+'")'}let Kt;function ah(){try{Kt=window.trustedTypes.createPolicy("iconify",{createHTML:e=>e})}catch{Kt=null}}function sh(e){return Kt===void 0&&ah(),Kt?Kt.createHTML(e):e}const zs={...js,inline:!1},ih={xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink","aria-hidden":!0,role:"img"},lh={display:"inline-block"},Nn={backgroundColor:"currentColor"},Hs={backgroundColor:"transparent"},pa={Image:"var(--svg)",Repeat:"no-repeat",Size:"100% 100%"},ma={WebkitMask:Nn,mask:Nn,background:Hs};for(const e in ma){const t=ma[e];for(const r in pa)t[e+r]=pa[r]}const uh={...zs,inline:!0};function ga(e){return e+(e.match(/^[-0-9.]+$/)?"px":"")}const ch=(e,t,r)=>{const n=t.inline?uh:zs,o=Xf(n,t),a=t.mode||"svg",s={},l=t.style||{},i={...a==="svg"?ih:{}};if(r){const m=Gr(r,!1,!0);if(m){const w=["iconify"],C=["provider","prefix"];for(const S of C)m[S]&&w.push("iconify--"+m[S]);i.className=w.join(" ")}}for(let m in t){const w=t[m];if(w!==void 0)switch(m){case"icon":case"style":case"children":case"onLoad":case"mode":case"ssr":break;case"_ref":i.ref=w;break;case"className":i[m]=(i[m]?i[m]+" ":"")+w;break;case"inline":case"hFlip":case"vFlip":o[m]=w===!0||w==="true"||w===1;break;case"flip":typeof w=="string"&&Zf(o,w);break;case"color":s.color=w;break;case"rotate":typeof w=="string"?o[m]=eh(w):typeof w=="number"&&(o[m]=w);break;case"ariaHidden":case"aria-hidden":w!==!0&&w!=="true"&&delete i["aria-hidden"];break;default:n[m]===void 0&&(i[m]=w)}}const u=Sf(e,o),d=u.attributes;if(o.inline&&(s.verticalAlign="-0.125em"),a==="svg"){i.style={...s,...l},Object.assign(i,d);let m=0,w=t.id;return typeof w=="string"&&(w=w.replace(/-/g,"_")),i.dangerouslySetInnerHTML={__html:sh(Cf(u.body,w?()=>w+"ID"+m++:"iconifyReact"))},g.createElement("svg",i)}const{body:c,width:h,height:b}=e,v=a==="mask"||(a==="bg"?!1:c.indexOf("currentColor")!==-1),R=th(c,{...d,width:h+"",height:b+""});return i.style={...s,"--svg":oh(R),width:ga(d.width),height:ga(d.height),...lh,...v?Nn:Hs,...l},g.createElement("span",i)};Fs(!0);Of("",Ff);if(typeof document<"u"&&typeof window<"u"){const e=window;if(e.IconifyPreload!==void 0){const t=e.IconifyPreload,r="Invalid IconifyPreload syntax.";typeof t=="object"&&t!==null&&(t instanceof Array?t:[t]).forEach(n=>{try{(typeof n!="object"||n===null||n instanceof Array||typeof n.icons!="object"||typeof n.prefix!="string"||!yf(n))&&console.error(r)}catch{console.error(r)}})}if(e.IconifyProviders!==void 0){const t=e.IconifyProviders;if(typeof t=="object"&&t!==null)for(let r in t){const n="IconifyProviders["+r+"] is invalid.";try{const o=t[r];if(typeof o!="object"||!o||o.resources===void 0)continue;Lf(r,o)||console.error(n)}catch{console.error(n)}}}}function Vs(e){const[t,r]=g.useState(!!e.ssr),[n,o]=g.useState({});function a(b){if(b){const v=e.icon;if(typeof v=="object")return{name:"",data:v};const R=la(v);if(R)return{name:v,data:R}}return{name:""}}const[s,l]=g.useState(a(!!e.ssr));function i(){const b=n.callback;b&&(b(),o({}))}function u(b){if(JSON.stringify(s)!==JSON.stringify(b))return i(),l(b),!0}function d(){var b;const v=e.icon;if(typeof v=="object"){u({name:"",data:v});return}const R=la(v);if(u({name:v,data:R}))if(R===void 0){const m=Yf([v],d);o({callback:m})}else R&&((b=e.onLoad)===null||b===void 0||b.call(e,v))}g.useEffect(()=>(r(!0),i),[]),g.useEffect(()=>{t&&d()},[e.icon,t]);const{name:c,data:h}=s;return h?ch({...to,...h},e,c):e.children?e.children:e.fallback?e.fallback:g.createElement("span",{})}const dh=g.forwardRef((e,t)=>Vs({...e,_ref:t}));g.forwardRef((e,t)=>Vs({inline:!0,...e,_ref:t}));const fh="/src/pages",hh=Object.assign({"/src/pages/Article/index.tsx":()=>V(()=>import("./index-BCXtus2y.js").then(e=>e._),__vite__mapDeps([0,1,2])),"/src/pages/Component/index.tsx":()=>V(()=>import("./index-CiJpZiBe.js").then(e=>e._),__vite__mapDeps([3,1,2])),"/src/pages/Component/permissiom-select-tree.tsx":()=>V(()=>import("./permissiom-select-tree-BHeUl-vp.js").then(e=>e._),__vite__mapDeps([4,1,2,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20])),"/src/pages/Component/server-card-playground.tsx":()=>V(()=>import("./server-card-playground-CXBsE500.js").then(e=>e._),__vite__mapDeps([21,1,2,22,6,23,5,24,25,26,27])),"/src/pages/CssPlayground/border-loading-playground.tsx":()=>V(()=>import("./border-loading-playground-VhUkW4w5.js").then(e=>e._),__vite__mapDeps([28,1,2])),"/src/pages/CssPlayground/index.tsx":()=>V(()=>import("./index-CoWXcXUt.js").then(e=>e._),__vite__mapDeps([29,1,2])),"/src/pages/Hero/about-project/index.tsx":()=>V(()=>import("./index-DCtNtNWe.js").then(e=>e._),__vite__mapDeps([30,1,2,26,6])),"/src/pages/Hero/index.tsx":()=>V(()=>import("./index-uxbcQK2J.js").then(e=>e._),__vite__mapDeps([31,1,2])),"/src/pages/Home/index.tsx":()=>V(()=>import("./index-CYDP3JK6.js").then(e=>e._),__vite__mapDeps([32,1,2,22,6,23,5,24,25,26,27])),"/src/pages/Layout/components/system-setting.tsx":()=>V(()=>import("./system-setting-BCL-5juB.js").then(e=>e._),__vite__mapDeps([33,1,2,34,35,6,5,15,9,14,10,36,37,38,7,8,11,12,26,39,40,41,17,18])),"/src/pages/Layout/components/system-tabs.tsx":()=>V(()=>import("./system-tabs-DoErleGg.js").then(e=>e._),__vite__mapDeps([42,1,2,5,6,26,38,7,8,9,10,11,12,39,43])),"/src/pages/Layout/index.tsx":()=>V(()=>import("./index-CsW3xZnI.js").then(e=>e._),__vite__mapDeps([44,1,2,45,13,12,5,6,14,9,8,15,34,35,10,36,37,46,47,11,48,18,19,39,43,20,49,27,50,7,33,38,26,40,41,17,24,42])),"/src/pages/Login/index.tsx":()=>V(()=>import("./index-BRJbtU0g.js").then(e=>e._),__vite__mapDeps([51,1,2,52,53,6,40,5,54,16,12,14,9,17,18,8,19])),"/src/pages/Login/login-form.tsx":()=>V(()=>import("./login-form-6hze7DT4.js").then(e=>e._),__vite__mapDeps([52,1,2,53,6,40,5,54,16,12,14,9,17,18,8,19])),"/src/pages/PermissionManagement/index.tsx":()=>V(()=>import("./index-dShf-qZ2.js").then(e=>e._),__vite__mapDeps([55,1,2])),"/src/pages/PermissionManagement/user-permission/column.tsx":()=>V(()=>import("./column-CNF2cE_S.js").then(e=>e._),__vite__mapDeps([56,1,2,5,6,26,46,12,14,9,47,11,10,36,48,18,8,15,19,38,57,50,58,59])),"/src/pages/PermissionManagement/user-permission/components/auto-complete-box.tsx":()=>V(()=>import("./auto-complete-box-kbhyGzIw.js").then(e=>e.b),__vite__mapDeps([60,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84])),"/src/pages/PermissionManagement/user-permission/components/select-parent-box.tsx":()=>V(()=>import("./select-parent-box-DUuLIPxS.js").then(e=>e._),__vite__mapDeps([61,1,2,6,5,35,15,9,14,10,36,48,18,49,19])),"/src/pages/PermissionManagement/user-permission/dialog.tsx":()=>V(()=>import("./auto-complete-box-kbhyGzIw.js").then(e=>e._),__vite__mapDeps([60,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84])),"/src/pages/PermissionManagement/user-permission/index.tsx":()=>V(()=>import("./auto-complete-box-kbhyGzIw.js").then(e=>e.a),__vite__mapDeps([60,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84])),"/src/pages/PermissionManagement/user-role/colums.tsx":()=>V(()=>import("./colums-B5o1wkEj.js").then(e=>e._),__vite__mapDeps([66,1,2,5,6,16,12,14,9,17,18,8,19,46,47,11,10,36,48,15,26,58,59,57])),"/src/pages/PermissionManagement/user-role/dialog.tsx":()=>V(()=>import("./dialog-DSOM4rc6.js").then(e=>e._),__vite__mapDeps([63,1,2,5,6,62,35,15,9,14,10,36,37,47,11,54,53,40,4,7,8,12,13,16,17,18,19,20])),"/src/pages/PermissionManagement/user-role/index.tsx":()=>V(()=>import("./index-CsShudox.js").then(e=>e._),__vite__mapDeps([67,1,2,64,57,6,5,20,58,66,16,12,14,9,17,18,8,19,46,47,11,10,36,48,15,26,59,65,54,63,62,35,37,53,40,4,7,13])),"/src/pages/Publish/index.tsx":()=>V(()=>import("./index-CbSxiwz2.js").then(e=>e._),__vite__mapDeps([68,1,2,45,13,12,5,6,14,9,8,15,34,35,10,36,37,46,47,11,48,18,19,39,43,20,49,27,50,7])),"/src/pages/Server/achrive-list/columns.tsx":()=>V(()=>import("./columns-Bks_ghfS.js").then(e=>e._),__vite__mapDeps([69,1,2,5,6,46,12,14,9,47,11,10,36,48,18,8,15,19,26,25,70,58])),"/src/pages/Server/achrive-list/index.tsx":()=>V(()=>import("./index-BqVn8FSZ.js").then(e=>e._),__vite__mapDeps([71,1,2,69,5,6,46,12,14,9,47,11,10,36,48,18,8,15,19,26,25,70,58,72,64,57,20])),"/src/pages/Server/baned-list/columns.tsx":()=>V(()=>import("./columns-_UrUPpXo.js").then(e=>e._),__vite__mapDeps([73,1,2,5,6,46,12,14,9,47,11,10,36,48,18,8,15,19,26,25,70,58])),"/src/pages/Server/baned-list/index.tsx":()=>V(()=>import("./index-C-3UUo7c.js").then(e=>e._),__vite__mapDeps([74,1,2,73,5,6,46,12,14,9,47,11,10,36,48,18,8,15,19,26,25,70,58,72,64,57,20])),"/src/pages/Server/index.tsx":()=>V(()=>import("./index-DXFeEy7g.js").then(e=>e._),__vite__mapDeps([75,1,2])),"/src/pages/Server/review-list/columns.tsx":()=>V(()=>import("./columns-QUGf2FQe.js").then(e=>e._),__vite__mapDeps([76,1,2,5,6,46,12,14,9,47,11,10,36,48,18,8,15,19,26,25,70,58])),"/src/pages/Server/review-list/index.tsx":()=>V(()=>import("./index-Fk45LKSJ.js").then(e=>e._),__vite__mapDeps([77,1,2,76,5,6,46,12,14,9,47,11,10,36,48,18,8,15,19,26,25,70,58,72,64,57,20])),"/src/pages/Test/test-icon-select-panel.tsx":()=>V(()=>import("./test-icon-select-panel-CM8B11xS.js").then(e=>e._),__vite__mapDeps([78,1,2,62,35,6,5,15,9,14,10,36,37,54,7,8,11,12])),"/src/pages/Tools/api-tool/index.tsx":()=>V(()=>import("./index-Cq0RLz-l.js").then(e=>e._),__vite__mapDeps([79,1,2,54,6,5,46,12,14,9,47,11,10,36,48,18,8,15,19,23,13])),"/src/pages/Tools/index.tsx":()=>V(()=>import("./index-DDkN25NJ.js").then(e=>e._),__vite__mapDeps([80,1,2])),"/src/pages/UserManagement/admin-user-management/colums.tsx":()=>V(()=>import("./colums-DZo9Badn.js").then(e=>e._),__vite__mapDeps([81,1,2,5,6,16,12,14,9,17,18,8,19,46,47,11,10,36,48,15,58,59,57])),"/src/pages/UserManagement/admin-user-management/dialog.tsx":()=>V(()=>import("./dialog-DhF9uMsq.js").then(e=>e._),__vite__mapDeps([82,1,2,5,6,62,35,15,9,14,10,36,37,41,12,17,18,54,53,40,23])),"/src/pages/UserManagement/admin-user-management/index.tsx":()=>V(()=>import("./index-CQdrasTd.js").then(e=>e._),__vite__mapDeps([83,1,2,64,57,6,5,20,58,81,16,12,14,9,17,18,8,19,46,47,11,10,36,48,15,59,82,62,35,37,41,54,53,40,23,65])),"/src/pages/UserManagement/index.tsx":()=>V(()=>import("./index-DYEGkPY0.js").then(e=>e._),__vite__mapDeps([84,1,2]))}),ph=e=>hh[`${fh}${e}`];function ao(e,t,r=[]){if(r.unshift(e.route),!e.parentId)return`/${r.join("/")}`;const n=t.find(o=>o.id===e.parentId);return n?ao(n,t,r):(console.warn(`没有找到 ${e.label} 的父节点 ${e.parentId} ，但${e.label} 确实声明了指向此父节点:`),`/${r.join("/")}`)}const qs=(e,t)=>{const{route:r,label:n,icon:o,order:a,hide:s,status:l}=e,i={path:r,meta:{label:n,key:t,hideMenu:!!s,disabled:l===Ls.DISABLE}};return a&&(i.order=a),i.meta&&o&&(i.meta.icon=he.jsx(dh,{icon:o})),i},mh=(e,t)=>{const r=qs(e,ao(e,t));r.meta&&(r.meta.hideTab=!0);const{parentId:n,children:o=[]}=e;return n||(r.element=he.jsx(g.Suspense,{fallback:he.jsx(ws,{}),children:he.jsx(yc,{})})),r.children=Ws(o,t),uf(o)||r.children.unshift({index:!0,element:he.jsx(fs,{to:o[0].route,replace:!0})}),r},gh=(e,t)=>{const r=qs(e,ao(e,t));if(e.component){const n=g.lazy(ph(e.component));r.element=he.jsx(g.Suspense,{fallback:he.jsx(ws,{}),children:he.jsx(n,{})})}return r};function Ws(e,t){return e.map(r=>r.type===Ds.CATALOGUE?mh(r,t):gh(r,t))}function yh(){const{userPermission:e}=Ad();return g.useMemo(()=>{if(!e)return[];const t=Os(e);return Ws(e,t)},[e])}const vh=g.createContext(null),yn={didCatch:!1,error:null};class bh extends g.Component{constructor(t){super(t),this.resetErrorBoundary=this.resetErrorBoundary.bind(this),this.state=yn}static getDerivedStateFromError(t){return{didCatch:!0,error:t}}resetErrorBoundary(){const{error:t}=this.state;if(t!==null){for(var r,n,o=arguments.length,a=new Array(o),s=0;s<o;s++)a[s]=arguments[s];(r=(n=this.props).onReset)===null||r===void 0||r.call(n,{args:a,reason:"imperative-api"}),this.setState(yn)}}componentDidCatch(t,r){var n,o;(n=(o=this.props).onError)===null||n===void 0||n.call(o,t,r)}componentDidUpdate(t,r){const{didCatch:n}=this.state,{resetKeys:o}=this.props;if(n&&r.error!==null&&wh(t.resetKeys,o)){var a,s;(a=(s=this.props).onReset)===null||a===void 0||a.call(s,{next:o,prev:t.resetKeys,reason:"keys"}),this.setState(yn)}}render(){const{children:t,fallbackRender:r,FallbackComponent:n,fallback:o}=this.props,{didCatch:a,error:s}=this.state;let l=t;if(a){const i={error:s,resetErrorBoundary:this.resetErrorBoundary};if(typeof r=="function")l=r(i);else if(n)l=g.createElement(n,i);else if(o!==void 0)l=o;else throw s}return g.createElement(vh.Provider,{value:{didCatch:a,error:s,resetErrorBoundary:this.resetErrorBoundary}},l)}}function wh(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];return e.length!==t.length||e.some((r,n)=>!Object.is(r,t[n]))}function Eh(e){return g.createElement(pc,{flushSync:ei.flushSync,...e})}const xh={VITE_APP_HOMEPAGE:"/hero/about-project"},Rh=g.lazy(()=>V(()=>import("./index-CsW3xZnI.js").then(e=>e._),__vite__mapDeps([44,1,2,45,13,12,5,6,14,9,8,15,34,35,10,36,37,46,47,11,48,18,19,39,43,20,49,27,50,7,33,38,26,40,41,17,24,42]))),Sh=g.lazy(()=>V(()=>import("./index-BRJbtU0g.js").then(e=>e._),__vite__mapDeps([51,1,2,52,53,6,40,5,54,16,12,14,9,17,18,8,19]))),{VITE_APP_HOMEPAGE:_h}=xh,Th={path:"/login",element:he.jsx(bh,{fallback:he.jsx(he.Fragment,{children:"出错啦！！！"}),children:he.jsx(Sh,{})})};function Ph(){const e=yh(),t={path:"/",element:he.jsx(kd,{children:he.jsx(Rh,{})}),children:[{index:!0,element:he.jsx(fs,{to:_h,replace:!0})},...e]},n=$c([Th,t]);return he.jsx(Eh,{router:n})}const Yh=Object.freeze(Object.defineProperty({__proto__:null,default:Ph},Symbol.toStringTag,{value:"Module"}));export{ws as B,dh as I,Vc as N,yc as O,Ls as P,Jh as T,bs as a,Ad as b,Yr as c,Lt as d,wa as e,Er as f,yh as g,pe as h,Id as i,Qh as j,Yh as k,Ot as r,wt as t,Xe as u};
