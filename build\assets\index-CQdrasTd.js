import{o as e}from"./index-Cq3zfZON.js";import{S as f}from"./index-C-scFdsV.js";import{C as D}from"./colums-DZo9Badn.js";import{i as U,j as _}from"./index-CLQUlq3d.js";import{U as x}from"./dialog-DhF9uMsq.js";import{S as j}from"./sakura-table-bar-DOjbc8KC.js";function O(){const{userListData:a,pagination:t,filterName:n,setFilterName:r}=U(),{handleCreateUser:o,handleEditUser:l,handleDeleteUser:s,handleOpenCreateDialog:i,handleOpenEditDialog:d,handleCloseDialog:m,handleDeleteSelected:u,rowSelection:c,selectedCount:p,setRowSelection:g,isCreateDialogOpen:h,isEditDialogOpen:C,editingItem:S}=_(),b=D({handleOpenEditDialog:d,handleDeleteUser:s});return e.jsxs("div",{className:"container mx-auto",children:[e.jsx(j,{enableSearch:!0,enableCreate:!0,enableSelected:!0,searchValue:n,selectCount:p,onSearchChange:r,onOpenCreateDialog:i,onDeleteItems:u,searchPlaceholder:"搜索名称",createButtonText:"创建用户"}),e.jsx(f,{columns:b,data:a?.users||[],serverPagination:t,rowSelection:c,enablePagination:!0,onRowSelectionChange:g}),e.jsx(x,{open:h||C,enableSelected:!0,updateUserItem:S,handleCreate:o,handleEdit:l,onClose:m})]})}const N=Object.freeze(Object.defineProperty({__proto__:null,default:O},Symbol.toStringTag,{value:"Module"}));export{N as _};
