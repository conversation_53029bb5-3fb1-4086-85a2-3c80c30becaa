
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

body {
  /* Removed overflow restrictions */
  height: 100vh;
  margin: 0;
  background-color: var(--color-bg);
  color: var(--color-text);
  
}

html,
#root {
  height: 100%;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  /* 保持原有的自定义变量 */
  --color-bg: oklch(0.21 0 0);
  --color-text: oklch(0.95 0 0);
  --color-primary: oklch(0.75 0.19 250);
  --color-primary-foreground: oklch(0.21 0 0);

  /* 添加完整的主题变量以匹配系统需求 */
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}
.sakura {
  /* 保持原有的自定义变量 */
  --color-bg: oklch(0.99 0.01 350);
  --color-text: oklch(0.36 0.09 350);
  --color-primary: oklch(0.77 0.19 350);
  --color-primary-foreground: oklch(0.99 0 0);

  /* 添加完整的主题变量以匹配系统需求 */
  --background: oklch(0.99 0.01 350);
  --foreground: oklch(0.36 0.09 350);
  --card: oklch(0.98 0.01 350);
  --card-foreground: oklch(0.36 0.09 350);
  --popover: oklch(0.98 0.01 350);
  --popover-foreground: oklch(0.36 0.09 350);
  --primary: oklch(0.77 0.19 350);
  --primary-foreground: oklch(0.99 0 0);
  --secondary: oklch(0.95 0.02 350);
  --secondary-foreground: oklch(0.36 0.09 350);
  --muted: oklch(0.95 0.02 350);
  --muted-foreground: oklch(0.55 0.06 350);
  --accent: oklch(0.95 0.02 350);
  --accent-foreground: oklch(0.36 0.09 350);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.90 0.03 350);
  --input: oklch(0.90 0.03 350);
  --ring: oklch(0.77 0.19 350);
  --chart-1: oklch(0.77 0.19 350);
  --chart-2: oklch(0.65 0.15 320);
  --chart-3: oklch(0.55 0.12 380);
  --chart-4: oklch(0.70 0.17 330);
  --chart-5: oklch(0.60 0.14 360);
  --sidebar: oklch(0.98 0.01 350);
  --sidebar-foreground: oklch(0.36 0.09 350);
  --sidebar-primary: oklch(0.77 0.19 350);
  --sidebar-primary-foreground: oklch(0.99 0 0);
  --sidebar-accent: oklch(0.95 0.02 350);
  --sidebar-accent-foreground: oklch(0.36 0.09 350);
  --sidebar-border: oklch(0.90 0.03 350);
  --sidebar-ring: oklch(0.77 0.19 350);
}

/* 新增 Blue 变量 */
.blue {
  /* 自定义变量 */
  --color-bg: oklch(0.97 0.02 220);
  --color-text: oklch(0.25 0.08 220);
  --color-primary: oklch(0.65 0.18 220);
  --color-primary-foreground: oklch(0.99 0 0);

  /* 完整的主题变量 */
  --background: oklch(0.97 0.02 220);
  --foreground: oklch(0.25 0.08 220);
  --card: oklch(0.96 0.02 220);
  --card-foreground: oklch(0.25 0.08 220);
  --popover: oklch(0.96 0.02 220);
  --popover-foreground: oklch(0.25 0.08 220);
  --primary: oklch(0.65 0.18 220);
  --primary-foreground: oklch(0.99 0 0);
  --secondary: oklch(0.93 0.03 220);
  --secondary-foreground: oklch(0.25 0.08 220);
  --muted: oklch(0.93 0.03 220);
  --muted-foreground: oklch(0.50 0.06 220);
  --accent: oklch(0.93 0.03 220);
  --accent-foreground: oklch(0.25 0.08 220);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.88 0.04 220);
  --input: oklch(0.88 0.04 220);
  --ring: oklch(0.65 0.18 220);
  --chart-1: oklch(0.65 0.18 220);
  --chart-2: oklch(0.55 0.15 200);
  --chart-3: oklch(0.45 0.12 240);
  --chart-4: oklch(0.60 0.16 210);
  --chart-5: oklch(0.50 0.14 230);
  --sidebar: oklch(0.96 0.02 220);
  --sidebar-foreground: oklch(0.25 0.08 220);
  --sidebar-primary: oklch(0.65 0.18 220);
  --sidebar-primary-foreground: oklch(0.99 0 0);
  --sidebar-accent: oklch(0.93 0.03 220);
  --sidebar-accent-foreground: oklch(0.25 0.08 220);
  --sidebar-border: oklch(0.88 0.04 220);
  --sidebar-ring: oklch(0.65 0.18 220);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@keyframes breathing {
  0% {
    opacity: 0.7;
    transform: scale(0.85);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.7;
    transform: scale(0.85);
  }
}

.animate-breathing {
  animation: breathing 2s ease-in-out infinite;
}

.shadow-green-glow {
  box-shadow: 0 0 8px 2px rgba(74, 222, 128, 0.6);
}

.shadow-red-glow {
  box-shadow: 0 0 8px 2px rgba(239, 68, 68, 0.4);
}
