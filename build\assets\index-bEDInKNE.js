import{j as o,o as c}from"./index-DTZYRUcV.js";import{c as I}from"./index-Dc_FVRD7.js";import{c as O,P as v}from"./button-D3EwPaXF.js";import{u as S}from"./index-_Qo_7fUN.js";import{u as D}from"./index-D2spak-M.js";import{u as M}from"./index-Mx6w-wA8.js";import{P as L}from"./index-DpyianfF.js";import{u as k}from"./index-Y7mrd1C4.js";var f="Collapsible",[F,V]=O(f),[B,g]=F(f),T=o.forwardRef((e,s)=>{const{__scopeCollapsible:r,open:a,defaultOpen:t,disabled:l,onOpenChange:i,...m}=e,[d,p]=S({prop:a,defaultProp:t??!1,onChange:i,caller:f});return c.jsx(B,{scope:r,disabled:l,contentId:k(),open:d,onOpenToggle:o.useCallback(()=>p(C=>!C),[p]),children:c.jsx(v.div,{"data-state":R(d),"data-disabled":l?"":void 0,...m,ref:s})})});T.displayName=f;var j="CollapsibleTrigger",w=o.forwardRef((e,s)=>{const{__scopeCollapsible:r,...a}=e,t=g(j,r);return c.jsx(v.button,{type:"button","aria-controls":t.contentId,"aria-expanded":t.open||!1,"data-state":R(t.open),"data-disabled":t.disabled?"":void 0,disabled:t.disabled,...a,ref:s,onClick:I(e.onClick,t.onOpenToggle)})});w.displayName=j;var x="CollapsibleContent",A=o.forwardRef((e,s)=>{const{forceMount:r,...a}=e,t=g(x,e.__scopeCollapsible);return c.jsx(L,{present:r||t.open,children:({present:l})=>c.jsx(G,{...a,ref:s,present:l})})});A.displayName=x;var G=o.forwardRef((e,s)=>{const{__scopeCollapsible:r,present:a,children:t,...l}=e,i=g(x,r),[m,d]=o.useState(a),p=o.useRef(null),C=M(s,p),h=o.useRef(0),P=h.current,y=o.useRef(0),N=y.current,b=i.open||m,E=o.useRef(b),u=o.useRef(void 0);return o.useEffect(()=>{const n=requestAnimationFrame(()=>E.current=!1);return()=>cancelAnimationFrame(n)},[]),D(()=>{const n=p.current;if(n){u.current=u.current||{transitionDuration:n.style.transitionDuration,animationName:n.style.animationName},n.style.transitionDuration="0s",n.style.animationName="none";const _=n.getBoundingClientRect();h.current=_.height,y.current=_.width,E.current||(n.style.transitionDuration=u.current.transitionDuration,n.style.animationName=u.current.animationName),d(a)}},[i.open,a]),c.jsx(v.div,{"data-state":R(i.open),"data-disabled":i.disabled?"":void 0,id:i.contentId,hidden:!b,...l,ref:C,style:{"--radix-collapsible-content-height":P?`${P}px`:void 0,"--radix-collapsible-content-width":N?`${N}px`:void 0,...e.style},children:b&&t})});function R(e){return e?"open":"closed"}var W=T,X=w,Y=A;export{Y as C,W as R,X as T,w as a,A as b,V as c};
