import{F as ne,j as p,o as e,R as C,I as L,l as De}from"./index-Cq3zfZON.js";import{I as ke}from"./input-BHGiuYUZ.js";import{c as se,P as D,B as P}from"./button-CTQLK12e.js";import{l as ce,m as $e,n as Ge,o as qe,I as Le,p as Ue,q as Ke,r as He,s as Ve,t as ze,P as Fe,G as Be,L as Ye,u as Je,v as We,S as Xe,w as Qe,x as Ze,y as er,z as rr,D as ee,a as re,b as te,B as ae,E as S}from"./dropdown-menu-BSc-dlG8.js";import{c as ie}from"./index-BBWlQkPG.js";import{u as le}from"./index-5UnhShgU.js";import{c as A}from"./index-Dc_FVRD7.js";import{u as de,c as I}from"./index-CGKXfO_7.js";import{u as T}from"./index-CnargNwx.js";import{u as Y}from"./index-MsHvSdph.js";import{C as tr,b as ar,c as or,a as nr}from"./card-B51fHEQ3.js";import{h as sr,d as cr}from"./index-CLQUlq3d.js";import{c as ue,R as ir,T as lr,C as dr}from"./index-Dl90smRv.js";/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ur=[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]],pr=ne("arrow-down",ur);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mr=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],xr=ne("chevron-down",mr);var E="Menubar",[V,fr,br]=ie(E),[pe,St]=se(E,[br,ce]),j=Ge(),me=ce(),[hr,J]=pe(E),xe=p.forwardRef((r,a)=>{const{__scopeMenubar:t,value:o,onValueChange:s,defaultValue:c,loop:m=!0,dir:i,...l}=r,b=le(i),u=me(t),[x,d]=Y({prop:o,onChange:s,defaultProp:c??"",caller:E}),[g,v]=p.useState(null);return e.jsx(hr,{scope:t,value:x,onMenuOpen:p.useCallback(n=>{d(n),v(n)},[d]),onMenuClose:p.useCallback(()=>d(""),[d]),onMenuToggle:p.useCallback(n=>{d(h=>h?"":n),v(n)},[d]),dir:b,loop:m,children:e.jsx(V.Provider,{scope:t,children:e.jsx(V.Slot,{scope:t,children:e.jsx($e,{asChild:!0,...u,orientation:"horizontal",loop:m,dir:b,currentTabStopId:g,onCurrentTabStopIdChange:v,children:e.jsx(D.div,{role:"menubar",...l,ref:a})})})})})});xe.displayName=E;var W="MenubarMenu",[gr,fe]=pe(W),be=r=>{const{__scopeMenubar:a,value:t,...o}=r,s=T(),c=t||s||"LEGACY_REACT_AUTO_VALUE",m=J(W,a),i=j(a),l=p.useRef(null),b=p.useRef(!1),u=m.value===c;return p.useEffect(()=>{u||(b.current=!1)},[u]),e.jsx(gr,{scope:a,value:c,triggerId:T(),triggerRef:l,contentId:T(),wasKeyboardTriggerOpenRef:b,children:e.jsx(qe,{...i,open:u,onOpenChange:x=>{x||m.onMenuClose()},modal:!1,dir:m.dir,...o})})};be.displayName=W;var z="MenubarTrigger",he=p.forwardRef((r,a)=>{const{__scopeMenubar:t,disabled:o=!1,...s}=r,c=me(t),m=j(t),i=J(z,t),l=fe(z,t),b=p.useRef(null),u=de(a,b,l.triggerRef),[x,d]=p.useState(!1),g=i.value===l.value;return e.jsx(V.ItemSlot,{scope:t,value:l.value,disabled:o,children:e.jsx(Le,{asChild:!0,...c,focusable:!o,tabStopId:l.value,children:e.jsx(Ue,{asChild:!0,...m,children:e.jsx(D.button,{type:"button",role:"menuitem",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":g,"aria-controls":g?l.contentId:void 0,"data-highlighted":x?"":void 0,"data-state":g?"open":"closed","data-disabled":o?"":void 0,disabled:o,...s,ref:u,onPointerDown:A(r.onPointerDown,v=>{!o&&v.button===0&&v.ctrlKey===!1&&(i.onMenuOpen(l.value),g||v.preventDefault())}),onPointerEnter:A(r.onPointerEnter,()=>{!!i.value&&!g&&(i.onMenuOpen(l.value),b.current?.focus())}),onKeyDown:A(r.onKeyDown,v=>{o||(["Enter"," "].includes(v.key)&&i.onMenuToggle(l.value),v.key==="ArrowDown"&&i.onMenuOpen(l.value),["Enter"," ","ArrowDown"].includes(v.key)&&(l.wasKeyboardTriggerOpenRef.current=!0,v.preventDefault()))}),onFocus:A(r.onFocus,()=>d(!0)),onBlur:A(r.onBlur,()=>d(!1))})})})})});he.displayName=z;var vr="MenubarPortal",ge=r=>{const{__scopeMenubar:a,...t}=r,o=j(a);return e.jsx(Fe,{...o,...t})};ge.displayName=vr;var F="MenubarContent",ve=p.forwardRef((r,a)=>{const{__scopeMenubar:t,align:o="start",...s}=r,c=j(t),m=J(F,t),i=fe(F,t),l=fr(t),b=p.useRef(!1);return e.jsx(Ke,{id:i.contentId,"aria-labelledby":i.triggerId,"data-radix-menubar-content":"",...c,...s,ref:a,align:o,onCloseAutoFocus:A(r.onCloseAutoFocus,u=>{!!!m.value&&!b.current&&i.triggerRef.current?.focus(),b.current=!1,u.preventDefault()}),onFocusOutside:A(r.onFocusOutside,u=>{const x=u.target;l().some(g=>g.ref.current?.contains(x))&&u.preventDefault()}),onInteractOutside:A(r.onInteractOutside,()=>{b.current=!0}),onEntryFocus:u=>{i.wasKeyboardTriggerOpenRef.current||u.preventDefault()},onKeyDown:A(r.onKeyDown,u=>{if(["ArrowRight","ArrowLeft"].includes(u.key)){const x=u.target,d=x.hasAttribute("data-radix-menubar-subtrigger"),g=x.closest("[data-radix-menubar-content]")!==u.currentTarget,n=(m.dir==="rtl"?"ArrowRight":"ArrowLeft")===u.key;if(!n&&d||g&&n)return;let f=l().filter(R=>!R.disabled).map(R=>R.value);n&&f.reverse();const y=f.indexOf(i.value);f=m.loop?qr(f,y+1):f.slice(y+1);const[N]=f;N&&m.onMenuOpen(N)}},{checkForDefaultPrevented:!1}),style:{...r.style,"--radix-menubar-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-menubar-content-available-width":"var(--radix-popper-available-width)","--radix-menubar-content-available-height":"var(--radix-popper-available-height)","--radix-menubar-trigger-width":"var(--radix-popper-anchor-width)","--radix-menubar-trigger-height":"var(--radix-popper-anchor-height)"}})});ve.displayName=F;var jr="MenubarGroup",Mr=p.forwardRef((r,a)=>{const{__scopeMenubar:t,...o}=r,s=j(t);return e.jsx(Be,{...s,...o,ref:a})});Mr.displayName=jr;var Cr="MenubarLabel",wr=p.forwardRef((r,a)=>{const{__scopeMenubar:t,...o}=r,s=j(t);return e.jsx(Ye,{...s,...o,ref:a})});wr.displayName=Cr;var Nr="MenubarItem",Ar=p.forwardRef((r,a)=>{const{__scopeMenubar:t,...o}=r,s=j(t);return e.jsx(Je,{...s,...o,ref:a})});Ar.displayName=Nr;var yr="MenubarCheckboxItem",Rr=p.forwardRef((r,a)=>{const{__scopeMenubar:t,...o}=r,s=j(t);return e.jsx(We,{...s,...o,ref:a})});Rr.displayName=yr;var _r="MenubarRadioGroup",je=p.forwardRef((r,a)=>{const{__scopeMenubar:t,...o}=r,s=j(t);return e.jsx(He,{...s,...o,ref:a})});je.displayName=_r;var Ir="MenubarRadioItem",Me=p.forwardRef((r,a)=>{const{__scopeMenubar:t,...o}=r,s=j(t);return e.jsx(Ve,{...s,...o,ref:a})});Me.displayName=Ir;var Sr="MenubarItemIndicator",Ce=p.forwardRef((r,a)=>{const{__scopeMenubar:t,...o}=r,s=j(t);return e.jsx(ze,{...s,...o,ref:a})});Ce.displayName=Sr;var Er="MenubarSeparator",Pr=p.forwardRef((r,a)=>{const{__scopeMenubar:t,...o}=r,s=j(t);return e.jsx(Xe,{...s,...o,ref:a})});Pr.displayName=Er;var Tr="MenubarArrow",Or=p.forwardRef((r,a)=>{const{__scopeMenubar:t,...o}=r,s=j(t);return e.jsx(Qe,{...s,...o,ref:a})});Or.displayName=Tr;var Dr="MenubarSubTrigger",kr=p.forwardRef((r,a)=>{const{__scopeMenubar:t,...o}=r,s=j(t);return e.jsx(Ze,{"data-radix-menubar-subtrigger":"",...s,...o,ref:a})});kr.displayName=Dr;var $r="MenubarSubContent",Gr=p.forwardRef((r,a)=>{const{__scopeMenubar:t,...o}=r,s=j(t);return e.jsx(er,{...s,"data-radix-menubar-content":"",...o,ref:a,style:{...r.style,"--radix-menubar-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-menubar-content-available-width":"var(--radix-popper-available-width)","--radix-menubar-content-available-height":"var(--radix-popper-available-height)","--radix-menubar-trigger-width":"var(--radix-popper-anchor-width)","--radix-menubar-trigger-height":"var(--radix-popper-anchor-height)"}})});Gr.displayName=$r;function qr(r,a){return r.map((t,o)=>r[(a+o)%r.length])}var Lr=xe,Ur=be,Kr=he,Hr=ge,Vr=ve,zr=je,Fr=Me,Br=Ce;function Yr({className:r,...a}){return e.jsx(Lr,{"data-slot":"menubar",className:I("bg-background flex h-9 items-center gap-1 rounded-md border p-1 shadow-xs",r),...a})}function Jr({...r}){return e.jsx(Ur,{"data-slot":"menubar-menu",...r})}function Wr({...r}){return e.jsx(Hr,{"data-slot":"menubar-portal",...r})}function Xr({...r}){return e.jsx(zr,{"data-slot":"menubar-radio-group",...r})}function Qr({className:r,...a}){return e.jsx(Kr,{"data-slot":"menubar-trigger",className:I("focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex items-center rounded-sm px-2 py-1 text-sm font-medium outline-hidden select-none",r),...a})}function Zr({className:r,align:a="start",alignOffset:t=-4,sideOffset:o=8,...s}){return e.jsx(Wr,{children:e.jsx(Vr,{"data-slot":"menubar-content",align:a,alignOffset:t,sideOffset:o,className:I("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[12rem] origin-(--radix-menubar-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-md",r),...s})})}function oe({className:r,children:a,...t}){return e.jsxs(Fr,{"data-slot":"menubar-radio-item",className:I("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-xs py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",r),...t,children:[e.jsx("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:e.jsx(Br,{children:e.jsx(rr,{className:"size-2 fill-current"})})}),a]})}function et(r){const a={};function t(o){for(const s in o)typeof o[s]=="string"?(a[s]&&console.warn(`Duplicate key detected: ${s}`),a[s]=o[s]):typeof o[s]=="object"&&o[s]!==null&&t(o[s])}return t(r),a}var w="Accordion",rt=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[X,tt,at]=ie(w),[k,Et]=se(w,[at,ue]),Q=ue(),we=C.forwardRef((r,a)=>{const{type:t,...o}=r,s=o,c=o;return e.jsx(X.Provider,{scope:r.__scopeAccordion,children:t==="multiple"?e.jsx(ct,{...c,ref:a}):e.jsx(st,{...s,ref:a})})});we.displayName=w;var[Ne,ot]=k(w),[Ae,nt]=k(w,{collapsible:!1}),st=C.forwardRef((r,a)=>{const{value:t,defaultValue:o,onValueChange:s=()=>{},collapsible:c=!1,...m}=r,[i,l]=Y({prop:t,defaultProp:o??"",onChange:s,caller:w});return e.jsx(Ne,{scope:r.__scopeAccordion,value:C.useMemo(()=>i?[i]:[],[i]),onItemOpen:l,onItemClose:C.useCallback(()=>c&&l(""),[c,l]),children:e.jsx(Ae,{scope:r.__scopeAccordion,collapsible:c,children:e.jsx(ye,{...m,ref:a})})})}),ct=C.forwardRef((r,a)=>{const{value:t,defaultValue:o,onValueChange:s=()=>{},...c}=r,[m,i]=Y({prop:t,defaultProp:o??[],onChange:s,caller:w}),l=C.useCallback(u=>i((x=[])=>[...x,u]),[i]),b=C.useCallback(u=>i((x=[])=>x.filter(d=>d!==u)),[i]);return e.jsx(Ne,{scope:r.__scopeAccordion,value:m,onItemOpen:l,onItemClose:b,children:e.jsx(Ae,{scope:r.__scopeAccordion,collapsible:!0,children:e.jsx(ye,{...c,ref:a})})})}),[it,$]=k(w),ye=C.forwardRef((r,a)=>{const{__scopeAccordion:t,disabled:o,dir:s,orientation:c="vertical",...m}=r,i=C.useRef(null),l=de(i,a),b=tt(t),x=le(s)==="ltr",d=A(r.onKeyDown,g=>{if(!rt.includes(g.key))return;const v=g.target,n=b().filter(q=>!q.ref.current?.disabled),h=n.findIndex(q=>q.ref.current===v),M=n.length;if(h===-1)return;g.preventDefault();let f=h;const y=0,N=M-1,R=()=>{f=h+1,f>N&&(f=y)},G=()=>{f=h-1,f<y&&(f=N)};switch(g.key){case"Home":f=y;break;case"End":f=N;break;case"ArrowRight":c==="horizontal"&&(x?R():G());break;case"ArrowDown":c==="vertical"&&R();break;case"ArrowLeft":c==="horizontal"&&(x?G():R());break;case"ArrowUp":c==="vertical"&&G();break}const Oe=f%M;n[Oe].ref.current?.focus()});return e.jsx(it,{scope:t,disabled:o,direction:s,orientation:c,children:e.jsx(X.Slot,{scope:t,children:e.jsx(D.div,{...m,"data-orientation":c,ref:l,onKeyDown:o?void 0:d})})})}),O="AccordionItem",[lt,Z]=k(O),Re=C.forwardRef((r,a)=>{const{__scopeAccordion:t,value:o,...s}=r,c=$(O,t),m=ot(O,t),i=Q(t),l=T(),b=o&&m.value.includes(o)||!1,u=c.disabled||r.disabled;return e.jsx(lt,{scope:t,open:b,disabled:u,triggerId:l,children:e.jsx(ir,{"data-orientation":c.orientation,"data-state":Te(b),...i,...s,ref:a,disabled:u,open:b,onOpenChange:x=>{x?m.onItemOpen(o):m.onItemClose(o)}})})});Re.displayName=O;var _e="AccordionHeader",Ie=C.forwardRef((r,a)=>{const{__scopeAccordion:t,...o}=r,s=$(w,t),c=Z(_e,t);return e.jsx(D.h3,{"data-orientation":s.orientation,"data-state":Te(c.open),"data-disabled":c.disabled?"":void 0,...o,ref:a})});Ie.displayName=_e;var B="AccordionTrigger",Se=C.forwardRef((r,a)=>{const{__scopeAccordion:t,...o}=r,s=$(w,t),c=Z(B,t),m=nt(B,t),i=Q(t);return e.jsx(X.ItemSlot,{scope:t,children:e.jsx(lr,{"aria-disabled":c.open&&!m.collapsible||void 0,"data-orientation":s.orientation,id:c.triggerId,...i,...o,ref:a})})});Se.displayName=B;var Ee="AccordionContent",Pe=C.forwardRef((r,a)=>{const{__scopeAccordion:t,...o}=r,s=$(w,t),c=Z(Ee,t),m=Q(t);return e.jsx(dr,{role:"region","aria-labelledby":c.triggerId,"data-orientation":s.orientation,...m,...o,ref:a,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...r.style}})});Pe.displayName=Ee;function Te(r){return r?"open":"closed"}var dt=we,ut=Re,pt=Ie,mt=Se,xt=Pe;function ft({...r}){return e.jsx(dt,{"data-slot":"accordion",...r})}function U({className:r,...a}){return e.jsx(ut,{"data-slot":"accordion-item",className:I("border-b last:border-b-0",r),...a})}function K({className:r,children:a,...t}){return e.jsx(pt,{className:"flex",children:e.jsxs(mt,{"data-slot":"accordion-trigger",className:I("focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180",r),...t,children:[a,e.jsx(xr,{className:"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200"})]})})}function H({className:r,children:a,...t}){return e.jsx(xt,{"data-slot":"accordion-content",className:"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm",...t,children:e.jsx("div",{className:I("pt-0 pb-4",r),children:a})})}const _=sr.create({baseURL:"http://localhost:5500",timeout:5e3});function bt(){const[r,a]=p.useState("GET"),[t,o]=p.useState(),[s,c]=p.useState(""),[m,i]=p.useState(""),[l,b]=p.useState("Response"),[u,x]=p.useState(""),d=L();p.useEffect(()=>{_.interceptors.request.use(n=>{const h=De.getState().userToken.token;h&&(n.headers.Authorization=`Bearer ${h}`),console.log(n);const M=n.url?`${n.baseURL}${n.url}`:n.baseURL||"",f=L.getState();f.setRequestUrl(M),f.setRequestMethod(n.method?.toUpperCase()||"GET");const y={};return n.headers&&Object.keys(n.headers).forEach(N=>{n.headers[N]!==void 0&&(y[N]=String(n.headers[N]))}),f.setRequestHeaders(y),n},n=>Promise.reject(n)),_.interceptors.response.use(n=>{console.log(n);const h=L.getState();h.setStatusCode(n.status.toString());const M={};return n.headers&&Object.keys(n.headers).forEach(f=>{M[f]=String(n.headers[f])}),h.setResponseHeaders(M),n})},[]),p.useMemo(()=>{const n=et(cr);o(n)},[]);const g=async()=>{try{let n;const h=r.toLowerCase(),M=m&&t?t[m]:s;switch(h){case"get":n=await _.get(M);break;case"post":n=await _.post(M);break;case"put":n=await _.put(M);break;case"delete":n=await _.delete(M);break;default:n=await _.get(M)}x(n.data)}catch(n){console.error("Request failed:",n),x({error:"Request failed"})}},v=n=>{i(n),t&&n in t&&c(t[n])};return e.jsxs("div",{className:"container  h-[calc(100vh-105px)] flex flex-col space-y-5",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{className:"flex space-x-1",children:[e.jsxs(ee,{children:[e.jsx(re,{asChild:!0,children:e.jsx(P,{variant:"outline",children:r})}),e.jsx(te,{className:"w-56",children:e.jsxs(ae,{value:r,onValueChange:a,children:[e.jsx(S,{value:"GET",children:"GET"}),e.jsx(S,{value:"POST",children:"POST"}),e.jsx(S,{value:"PUT",children:"PUT"}),e.jsx(S,{value:"DELETE",children:"DELETE"})]})})]}),e.jsx(ke,{value:s,onChange:n=>{c(n.target.value),i("")}}),e.jsxs(ee,{children:[e.jsx(re,{asChild:!0,children:e.jsx(P,{size:"icon",children:e.jsx(pr,{})})}),e.jsx(te,{className:"w-auto",children:e.jsx(ae,{value:m,onValueChange:v,children:t&&Object.entries(t).map(([n,h])=>e.jsxs(S,{value:n,children:[e.jsx(P,{variant:"secondary",children:n})," ",h]},n))})})]}),e.jsx(P,{variant:"destructive",onClick:g,children:" 发送 "})]}),e.jsx("div",{})]}),e.jsxs("div",{children:[e.jsx(Yr,{children:e.jsxs(Jr,{children:[e.jsx(Qr,{children:"返回内容"}),e.jsx(Zr,{children:e.jsxs(Xr,{value:l,onValueChange:b,children:[e.jsx(oe,{value:"Response",children:"Response"}),e.jsx(oe,{value:"Header",children:"Header"})]})})]})}),e.jsxs(tr,{children:[e.jsx(ar,{children:e.jsx(or,{children:l})}),e.jsxs(nr,{children:[l==="Response"&&e.jsx("div",{className:"max-h-96 overflow-auto",children:u?e.jsx("pre",{className:"bg-gray-100 p-4 rounded-md text-sm overflow-x-auto whitespace-pre-wrap",children:e.jsx("code",{className:"language-json",children:typeof u=="string"?u:JSON.stringify(u,null,2)})}):e.jsx("div",{className:"text-gray-500 text-center py-8",children:"暂无响应数据"})}),l==="Header"&&e.jsxs(ft,{type:"multiple",className:"w-full",children:[e.jsxs(U,{value:"item-1",children:[e.jsx(K,{className:"text-2xl",children:"通用"}),e.jsx(H,{className:"flex flex-col gap-4 text-balance",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center",children:[e.jsx("span",{className:"font-semibold text-sm min-w-[120px] text-blue-600",children:"Request URL:"}),e.jsx("span",{className:"text-sm text-gray-700 break-all",children:d.requestUrl||"暂无"})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center",children:[e.jsx("span",{className:"font-semibold text-sm min-w-[120px] text-blue-600",children:"Request Method:"}),e.jsx("span",{className:"text-sm text-gray-700",children:e.jsx("span",{className:`px-2 py-1 rounded text-xs font-medium ${d.requestMethod==="GET"?"bg-green-100 text-green-800":d.requestMethod==="POST"?"bg-blue-100 text-blue-800":d.requestMethod==="PUT"?"bg-yellow-100 text-yellow-800":d.requestMethod==="DELETE"?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:d.requestMethod||"N/A"})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center",children:[e.jsx("span",{className:"font-semibold text-sm min-w-[120px] text-blue-600",children:"Status Code:"}),e.jsx("span",{className:"text-sm text-gray-700",children:e.jsx("span",{className:`px-2 py-1 rounded text-xs font-medium ${d.statusCode>=200&&d.statusCode<300?"bg-green-100 text-green-800":d.statusCode>=300&&d.statusCode<400?"bg-yellow-100 text-yellow-800":d.statusCode>=400?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:d.statusCode||"N/A"})})]})]})})]}),e.jsxs(U,{value:"item-2",children:[e.jsx(K,{className:"text-2xl",children:"请求头"}),e.jsx(H,{className:"flex flex-col gap-4 text-balance",children:Object.keys(d.requestHeaders).length>0?e.jsx("div",{className:"space-y-2",children:Object.entries(d.requestHeaders).map(([n,h])=>e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center pb-2",children:[e.jsxs("span",{className:"font-semibold text-sm min-w-[150px] text-blue-600",children:[n,":"]}),e.jsx("span",{className:"text-sm text-gray-700 break-all",children:h})]},n))}):e.jsx("p",{className:"text-gray-500 text-center py-4",children:"暂无请求头信息"})})]}),e.jsxs(U,{value:"item-3",children:[e.jsx(K,{className:"text-2xl",children:"响应头"}),e.jsx(H,{className:"flex flex-col gap-4 text-balance",children:Object.keys(d.responseHeaders).length>0?e.jsx("div",{className:"space-y-2",children:Object.entries(d.responseHeaders).map(([n,h])=>e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center pb-2",children:[e.jsxs("span",{className:"font-semibold text-sm min-w-[150px] text-green-600",children:[n,":"]}),e.jsx("span",{className:"text-sm text-gray-700 break-all",children:h})]},n))}):e.jsx("p",{className:"text-gray-500 text-center py-4",children:"暂无响应头信息"})})]})]})]})]})]})]})}const Pt=Object.freeze(Object.defineProperty({__proto__:null,default:bt},Symbol.toStringTag,{value:"Module"}));export{Pt as _};
