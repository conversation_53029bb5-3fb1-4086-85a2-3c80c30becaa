import{j as S,v as Ut,o as Ct}from"./index-DTZYRUcV.js";import{P as Kt}from"./button-D3EwPaXF.js";const Gt=["top","right","bottom","left"],U=Math.min,F=Math.max,st=Math.round,it=Math.floor,z=t=>({x:t,y:t}),Jt={left:"right",right:"left",bottom:"top",top:"bottom"},Qt={start:"end",end:"start"};function gt(t,e,n){return F(t,U(e,n))}function Y(t,e){return typeof t=="function"?t(e):t}function q(t){return t.split("-")[0]}function Z(t){return t.split("-")[1]}function xt(t){return t==="x"?"y":"x"}function yt(t){return t==="y"?"height":"width"}const Zt=new Set(["top","bottom"]);function _(t){return Zt.has(q(t))?"y":"x"}function vt(t){return xt(_(t))}function te(t,e,n){n===void 0&&(n=!1);const o=Z(t),i=vt(t),r=yt(i);let s=i==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return e.reference[r]>e.floating[r]&&(s=ct(s)),[s,ct(s)]}function ee(t){const e=ct(t);return[pt(t),e,pt(e)]}function pt(t){return t.replace(/start|end/g,e=>Qt[e])}const St=["left","right"],Et=["right","left"],ne=["top","bottom"],oe=["bottom","top"];function ie(t,e,n){switch(t){case"top":case"bottom":return n?e?Et:St:e?St:Et;case"left":case"right":return e?ne:oe;default:return[]}}function re(t,e,n,o){const i=Z(t);let r=ie(q(t),n==="start",o);return i&&(r=r.map(s=>s+"-"+i),e&&(r=r.concat(r.map(pt)))),r}function ct(t){return t.replace(/left|right|bottom|top/g,e=>Jt[e])}function se(t){return{top:0,right:0,bottom:0,left:0,...t}}function Bt(t){return typeof t!="number"?se(t):{top:t,right:t,bottom:t,left:t}}function lt(t){const{x:e,y:n,width:o,height:i}=t;return{width:o,height:i,top:n,left:e,right:e+o,bottom:n+i,x:e,y:n}}function Pt(t,e,n){let{reference:o,floating:i}=t;const r=_(e),s=vt(e),c=yt(s),f=q(e),l=r==="y",a=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,m=o[c]/2-i[c]/2;let u;switch(f){case"top":u={x:a,y:o.y-i.height};break;case"bottom":u={x:a,y:o.y+o.height};break;case"right":u={x:o.x+o.width,y:d};break;case"left":u={x:o.x-i.width,y:d};break;default:u={x:o.x,y:o.y}}switch(Z(e)){case"start":u[s]-=m*(n&&l?-1:1);break;case"end":u[s]+=m*(n&&l?-1:1);break}return u}const ce=async(t,e,n)=>{const{placement:o="bottom",strategy:i="absolute",middleware:r=[],platform:s}=n,c=r.filter(Boolean),f=await(s.isRTL==null?void 0:s.isRTL(e));let l=await s.getElementRects({reference:t,floating:e,strategy:i}),{x:a,y:d}=Pt(l,o,f),m=o,u={},h=0;for(let g=0;g<c.length;g++){const{name:p,fn:w}=c[g],{x,y:v,data:b,reset:y}=await w({x:a,y:d,initialPlacement:o,placement:m,strategy:i,middlewareData:u,rects:l,platform:s,elements:{reference:t,floating:e}});a=x??a,d=v??d,u={...u,[p]:{...u[p],...b}},y&&h<=50&&(h++,typeof y=="object"&&(y.placement&&(m=y.placement),y.rects&&(l=y.rects===!0?await s.getElementRects({reference:t,floating:e,strategy:i}):y.rects),{x:a,y:d}=Pt(l,m,f)),g=-1)}return{x:a,y:d,placement:m,strategy:i,middlewareData:u}};async function et(t,e){var n;e===void 0&&(e={});const{x:o,y:i,platform:r,rects:s,elements:c,strategy:f}=t,{boundary:l="clippingAncestors",rootBoundary:a="viewport",elementContext:d="floating",altBoundary:m=!1,padding:u=0}=Y(e,t),h=Bt(u),p=c[m?d==="floating"?"reference":"floating":d],w=lt(await r.getClippingRect({element:(n=await(r.isElement==null?void 0:r.isElement(p)))==null||n?p:p.contextElement||await(r.getDocumentElement==null?void 0:r.getDocumentElement(c.floating)),boundary:l,rootBoundary:a,strategy:f})),x=d==="floating"?{x:o,y:i,width:s.floating.width,height:s.floating.height}:s.reference,v=await(r.getOffsetParent==null?void 0:r.getOffsetParent(c.floating)),b=await(r.isElement==null?void 0:r.isElement(v))?await(r.getScale==null?void 0:r.getScale(v))||{x:1,y:1}:{x:1,y:1},y=lt(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:x,offsetParent:v,strategy:f}):x);return{top:(w.top-y.top+h.top)/b.y,bottom:(y.bottom-w.bottom+h.bottom)/b.y,left:(w.left-y.left+h.left)/b.x,right:(y.right-w.right+h.right)/b.x}}const le=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:o,placement:i,rects:r,platform:s,elements:c,middlewareData:f}=e,{element:l,padding:a=0}=Y(t,e)||{};if(l==null)return{};const d=Bt(a),m={x:n,y:o},u=vt(i),h=yt(u),g=await s.getDimensions(l),p=u==="y",w=p?"top":"left",x=p?"bottom":"right",v=p?"clientHeight":"clientWidth",b=r.reference[h]+r.reference[u]-m[u]-r.floating[h],y=m[u]-r.reference[u],O=await(s.getOffsetParent==null?void 0:s.getOffsetParent(l));let R=O?O[v]:0;(!R||!await(s.isElement==null?void 0:s.isElement(O)))&&(R=c.floating[v]||r.floating[h]);const L=b/2-y/2,B=R/2-g[h]/2-1,P=U(d[w],B),N=U(d[x],B),M=P,D=R-g[h]-N,C=R/2-g[h]/2+L,H=gt(M,C,D),E=!f.arrow&&Z(i)!=null&&C!==H&&r.reference[h]/2-(C<M?P:N)-g[h]/2<0,T=E?C<M?C-M:C-D:0;return{[u]:m[u]+T,data:{[u]:H,centerOffset:C-H-T,...E&&{alignmentOffset:T}},reset:E}}}),fe=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var n,o;const{placement:i,middlewareData:r,rects:s,initialPlacement:c,platform:f,elements:l}=e,{mainAxis:a=!0,crossAxis:d=!0,fallbackPlacements:m,fallbackStrategy:u="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:g=!0,...p}=Y(t,e);if((n=r.arrow)!=null&&n.alignmentOffset)return{};const w=q(i),x=_(c),v=q(c)===c,b=await(f.isRTL==null?void 0:f.isRTL(l.floating)),y=m||(v||!g?[ct(c)]:ee(c)),O=h!=="none";!m&&O&&y.push(...re(c,g,h,b));const R=[c,...y],L=await et(e,p),B=[];let P=((o=r.flip)==null?void 0:o.overflows)||[];if(a&&B.push(L[w]),d){const C=te(i,s,b);B.push(L[C[0]],L[C[1]])}if(P=[...P,{placement:i,overflows:B}],!B.every(C=>C<=0)){var N,M;const C=(((N=r.flip)==null?void 0:N.index)||0)+1,H=R[C];if(H&&(!(d==="alignment"?x!==_(H):!1)||P.every(A=>A.overflows[0]>0&&_(A.placement)===x)))return{data:{index:C,overflows:P},reset:{placement:H}};let E=(M=P.filter(T=>T.overflows[0]<=0).sort((T,A)=>T.overflows[1]-A.overflows[1])[0])==null?void 0:M.placement;if(!E)switch(u){case"bestFit":{var D;const T=(D=P.filter(A=>{if(O){const k=_(A.placement);return k===x||k==="y"}return!0}).map(A=>[A.placement,A.overflows.filter(k=>k>0).reduce((k,X)=>k+X,0)]).sort((A,k)=>A[1]-k[1])[0])==null?void 0:D[0];T&&(E=T);break}case"initialPlacement":E=c;break}if(i!==E)return{reset:{placement:E}}}return{}}}};function Lt(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function Dt(t){return Gt.some(e=>t[e]>=0)}const ae=function(t){return t===void 0&&(t={}),{name:"hide",options:t,async fn(e){const{rects:n}=e,{strategy:o="referenceHidden",...i}=Y(t,e);switch(o){case"referenceHidden":{const r=await et(e,{...i,elementContext:"reference"}),s=Lt(r,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:Dt(s)}}}case"escaped":{const r=await et(e,{...i,altBoundary:!0}),s=Lt(r,n.floating);return{data:{escapedOffsets:s,escaped:Dt(s)}}}default:return{}}}}},Nt=new Set(["left","top"]);async function ue(t,e){const{placement:n,platform:o,elements:i}=t,r=await(o.isRTL==null?void 0:o.isRTL(i.floating)),s=q(n),c=Z(n),f=_(n)==="y",l=Nt.has(s)?-1:1,a=r&&f?-1:1,d=Y(e,t);let{mainAxis:m,crossAxis:u,alignmentAxis:h}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return c&&typeof h=="number"&&(u=c==="end"?h*-1:h),f?{x:u*a,y:m*l}:{x:m*l,y:u*a}}const de=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(e){var n,o;const{x:i,y:r,placement:s,middlewareData:c}=e,f=await ue(e,t);return s===((n=c.offset)==null?void 0:n.placement)&&(o=c.arrow)!=null&&o.alignmentOffset?{}:{x:i+f.x,y:r+f.y,data:{...f,placement:s}}}}},me=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:o,placement:i}=e,{mainAxis:r=!0,crossAxis:s=!1,limiter:c={fn:p=>{let{x:w,y:x}=p;return{x:w,y:x}}},...f}=Y(t,e),l={x:n,y:o},a=await et(e,f),d=_(q(i)),m=xt(d);let u=l[m],h=l[d];if(r){const p=m==="y"?"top":"left",w=m==="y"?"bottom":"right",x=u+a[p],v=u-a[w];u=gt(x,u,v)}if(s){const p=d==="y"?"top":"left",w=d==="y"?"bottom":"right",x=h+a[p],v=h-a[w];h=gt(x,h,v)}const g=c.fn({...e,[m]:u,[d]:h});return{...g,data:{x:g.x-n,y:g.y-o,enabled:{[m]:r,[d]:s}}}}}},he=function(t){return t===void 0&&(t={}),{options:t,fn(e){const{x:n,y:o,placement:i,rects:r,middlewareData:s}=e,{offset:c=0,mainAxis:f=!0,crossAxis:l=!0}=Y(t,e),a={x:n,y:o},d=_(i),m=xt(d);let u=a[m],h=a[d];const g=Y(c,e),p=typeof g=="number"?{mainAxis:g,crossAxis:0}:{mainAxis:0,crossAxis:0,...g};if(f){const v=m==="y"?"height":"width",b=r.reference[m]-r.floating[v]+p.mainAxis,y=r.reference[m]+r.reference[v]-p.mainAxis;u<b?u=b:u>y&&(u=y)}if(l){var w,x;const v=m==="y"?"width":"height",b=Nt.has(q(i)),y=r.reference[d]-r.floating[v]+(b&&((w=s.offset)==null?void 0:w[d])||0)+(b?0:p.crossAxis),O=r.reference[d]+r.reference[v]+(b?0:((x=s.offset)==null?void 0:x[d])||0)-(b?p.crossAxis:0);h<y?h=y:h>O&&(h=O)}return{[m]:u,[d]:h}}}},ge=function(t){return t===void 0&&(t={}),{name:"size",options:t,async fn(e){var n,o;const{placement:i,rects:r,platform:s,elements:c}=e,{apply:f=()=>{},...l}=Y(t,e),a=await et(e,l),d=q(i),m=Z(i),u=_(i)==="y",{width:h,height:g}=r.floating;let p,w;d==="top"||d==="bottom"?(p=d,w=m===(await(s.isRTL==null?void 0:s.isRTL(c.floating))?"start":"end")?"left":"right"):(w=d,p=m==="end"?"top":"bottom");const x=g-a.top-a.bottom,v=h-a.left-a.right,b=U(g-a[p],x),y=U(h-a[w],v),O=!e.middlewareData.shift;let R=b,L=y;if((n=e.middlewareData.shift)!=null&&n.enabled.x&&(L=v),(o=e.middlewareData.shift)!=null&&o.enabled.y&&(R=x),O&&!m){const P=F(a.left,0),N=F(a.right,0),M=F(a.top,0),D=F(a.bottom,0);u?L=h-2*(P!==0||N!==0?P+N:F(a.left,a.right)):R=g-2*(M!==0||D!==0?M+D:F(a.top,a.bottom))}await f({...e,availableWidth:L,availableHeight:R});const B=await s.getDimensions(c.floating);return h!==B.width||g!==B.height?{reset:{rects:!0}}:{}}}};function at(){return typeof window<"u"}function tt(t){return Wt(t)?(t.nodeName||"").toLowerCase():"#document"}function $(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function I(t){var e;return(e=(Wt(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function Wt(t){return at()?t instanceof Node||t instanceof $(t).Node:!1}function W(t){return at()?t instanceof Element||t instanceof $(t).Element:!1}function j(t){return at()?t instanceof HTMLElement||t instanceof $(t).HTMLElement:!1}function Tt(t){return!at()||typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof $(t).ShadowRoot}const pe=new Set(["inline","contents"]);function ot(t){const{overflow:e,overflowX:n,overflowY:o,display:i}=V(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!pe.has(i)}const we=new Set(["table","td","th"]);function xe(t){return we.has(tt(t))}const ye=[":popover-open",":modal"];function ut(t){return ye.some(e=>{try{return t.matches(e)}catch{return!1}})}const ve=["transform","translate","scale","rotate","perspective"],be=["transform","translate","scale","rotate","perspective","filter"],Ae=["paint","layout","strict","content"];function bt(t){const e=At(),n=W(t)?V(t):t;return ve.some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!e&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!e&&(n.filter?n.filter!=="none":!1)||be.some(o=>(n.willChange||"").includes(o))||Ae.some(o=>(n.contain||"").includes(o))}function Re(t){let e=K(t);for(;j(e)&&!Q(e);){if(bt(e))return e;if(ut(e))return null;e=K(e)}return null}function At(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const Oe=new Set(["html","body","#document"]);function Q(t){return Oe.has(tt(t))}function V(t){return $(t).getComputedStyle(t)}function dt(t){return W(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function K(t){if(tt(t)==="html")return t;const e=t.assignedSlot||t.parentNode||Tt(t)&&t.host||I(t);return Tt(e)?e.host:e}function Vt(t){const e=K(t);return Q(e)?t.ownerDocument?t.ownerDocument.body:t.body:j(e)&&ot(e)?e:Vt(e)}function nt(t,e,n){var o;e===void 0&&(e=[]),n===void 0&&(n=!0);const i=Vt(t),r=i===((o=t.ownerDocument)==null?void 0:o.body),s=$(i);if(r){const c=wt(s);return e.concat(s,s.visualViewport||[],ot(i)?i:[],c&&n?nt(c):[])}return e.concat(i,nt(i,[],n))}function wt(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function Ht(t){const e=V(t);let n=parseFloat(e.width)||0,o=parseFloat(e.height)||0;const i=j(t),r=i?t.offsetWidth:n,s=i?t.offsetHeight:o,c=st(n)!==r||st(o)!==s;return c&&(n=r,o=s),{width:n,height:o,$:c}}function Rt(t){return W(t)?t:t.contextElement}function J(t){const e=Rt(t);if(!j(e))return z(1);const n=e.getBoundingClientRect(),{width:o,height:i,$:r}=Ht(e);let s=(r?st(n.width):n.width)/o,c=(r?st(n.height):n.height)/i;return(!s||!Number.isFinite(s))&&(s=1),(!c||!Number.isFinite(c))&&(c=1),{x:s,y:c}}const Ce=z(0);function _t(t){const e=$(t);return!At()||!e.visualViewport?Ce:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function Se(t,e,n){return e===void 0&&(e=!1),!n||e&&n!==$(t)?!1:e}function G(t,e,n,o){e===void 0&&(e=!1),n===void 0&&(n=!1);const i=t.getBoundingClientRect(),r=Rt(t);let s=z(1);e&&(o?W(o)&&(s=J(o)):s=J(t));const c=Se(r,n,o)?_t(r):z(0);let f=(i.left+c.x)/s.x,l=(i.top+c.y)/s.y,a=i.width/s.x,d=i.height/s.y;if(r){const m=$(r),u=o&&W(o)?$(o):o;let h=m,g=wt(h);for(;g&&o&&u!==h;){const p=J(g),w=g.getBoundingClientRect(),x=V(g),v=w.left+(g.clientLeft+parseFloat(x.paddingLeft))*p.x,b=w.top+(g.clientTop+parseFloat(x.paddingTop))*p.y;f*=p.x,l*=p.y,a*=p.x,d*=p.y,f+=v,l+=b,h=$(g),g=wt(h)}}return lt({width:a,height:d,x:f,y:l})}function Ot(t,e){const n=dt(t).scrollLeft;return e?e.left+n:G(I(t)).left+n}function zt(t,e,n){n===void 0&&(n=!1);const o=t.getBoundingClientRect(),i=o.left+e.scrollLeft-(n?0:Ot(t,o)),r=o.top+e.scrollTop;return{x:i,y:r}}function Ee(t){let{elements:e,rect:n,offsetParent:o,strategy:i}=t;const r=i==="fixed",s=I(o),c=e?ut(e.floating):!1;if(o===s||c&&r)return n;let f={scrollLeft:0,scrollTop:0},l=z(1);const a=z(0),d=j(o);if((d||!d&&!r)&&((tt(o)!=="body"||ot(s))&&(f=dt(o)),j(o))){const u=G(o);l=J(o),a.x=u.x+o.clientLeft,a.y=u.y+o.clientTop}const m=s&&!d&&!r?zt(s,f,!0):z(0);return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-f.scrollLeft*l.x+a.x+m.x,y:n.y*l.y-f.scrollTop*l.y+a.y+m.y}}function Pe(t){return Array.from(t.getClientRects())}function Le(t){const e=I(t),n=dt(t),o=t.ownerDocument.body,i=F(e.scrollWidth,e.clientWidth,o.scrollWidth,o.clientWidth),r=F(e.scrollHeight,e.clientHeight,o.scrollHeight,o.clientHeight);let s=-n.scrollLeft+Ot(t);const c=-n.scrollTop;return V(o).direction==="rtl"&&(s+=F(e.clientWidth,o.clientWidth)-i),{width:i,height:r,x:s,y:c}}function De(t,e){const n=$(t),o=I(t),i=n.visualViewport;let r=o.clientWidth,s=o.clientHeight,c=0,f=0;if(i){r=i.width,s=i.height;const l=At();(!l||l&&e==="fixed")&&(c=i.offsetLeft,f=i.offsetTop)}return{width:r,height:s,x:c,y:f}}const Te=new Set(["absolute","fixed"]);function Me(t,e){const n=G(t,!0,e==="fixed"),o=n.top+t.clientTop,i=n.left+t.clientLeft,r=j(t)?J(t):z(1),s=t.clientWidth*r.x,c=t.clientHeight*r.y,f=i*r.x,l=o*r.y;return{width:s,height:c,x:f,y:l}}function Mt(t,e,n){let o;if(e==="viewport")o=De(t,n);else if(e==="document")o=Le(I(t));else if(W(e))o=Me(e,n);else{const i=_t(t);o={x:e.x-i.x,y:e.y-i.y,width:e.width,height:e.height}}return lt(o)}function jt(t,e){const n=K(t);return n===e||!W(n)||Q(n)?!1:V(n).position==="fixed"||jt(n,e)}function ke(t,e){const n=e.get(t);if(n)return n;let o=nt(t,[],!1).filter(c=>W(c)&&tt(c)!=="body"),i=null;const r=V(t).position==="fixed";let s=r?K(t):t;for(;W(s)&&!Q(s);){const c=V(s),f=bt(s);!f&&c.position==="fixed"&&(i=null),(r?!f&&!i:!f&&c.position==="static"&&!!i&&Te.has(i.position)||ot(s)&&!f&&jt(t,s))?o=o.filter(a=>a!==s):i=c,s=K(s)}return e.set(t,o),o}function Fe(t){let{element:e,boundary:n,rootBoundary:o,strategy:i}=t;const s=[...n==="clippingAncestors"?ut(e)?[]:ke(e,this._c):[].concat(n),o],c=s[0],f=s.reduce((l,a)=>{const d=Mt(e,a,i);return l.top=F(d.top,l.top),l.right=U(d.right,l.right),l.bottom=U(d.bottom,l.bottom),l.left=F(d.left,l.left),l},Mt(e,c,i));return{width:f.right-f.left,height:f.bottom-f.top,x:f.left,y:f.top}}function $e(t){const{width:e,height:n}=Ht(t);return{width:e,height:n}}function Be(t,e,n){const o=j(e),i=I(e),r=n==="fixed",s=G(t,!0,r,e);let c={scrollLeft:0,scrollTop:0};const f=z(0);function l(){f.x=Ot(i)}if(o||!o&&!r)if((tt(e)!=="body"||ot(i))&&(c=dt(e)),o){const u=G(e,!0,r,e);f.x=u.x+e.clientLeft,f.y=u.y+e.clientTop}else i&&l();r&&!o&&i&&l();const a=i&&!o&&!r?zt(i,c):z(0),d=s.left+c.scrollLeft-f.x-a.x,m=s.top+c.scrollTop-f.y-a.y;return{x:d,y:m,width:s.width,height:s.height}}function mt(t){return V(t).position==="static"}function kt(t,e){if(!j(t)||V(t).position==="fixed")return null;if(e)return e(t);let n=t.offsetParent;return I(t)===n&&(n=n.ownerDocument.body),n}function It(t,e){const n=$(t);if(ut(t))return n;if(!j(t)){let i=K(t);for(;i&&!Q(i);){if(W(i)&&!mt(i))return i;i=K(i)}return n}let o=kt(t,e);for(;o&&xe(o)&&mt(o);)o=kt(o,e);return o&&Q(o)&&mt(o)&&!bt(o)?n:o||Re(t)||n}const Ne=async function(t){const e=this.getOffsetParent||It,n=this.getDimensions,o=await n(t.floating);return{reference:Be(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function We(t){return V(t).direction==="rtl"}const Ve={convertOffsetParentRelativeRectToViewportRelativeRect:Ee,getDocumentElement:I,getClippingRect:Fe,getOffsetParent:It,getElementRects:Ne,getClientRects:Pe,getDimensions:$e,getScale:J,isElement:W,isRTL:We};function Yt(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function He(t,e){let n=null,o;const i=I(t);function r(){var c;clearTimeout(o),(c=n)==null||c.disconnect(),n=null}function s(c,f){c===void 0&&(c=!1),f===void 0&&(f=1),r();const l=t.getBoundingClientRect(),{left:a,top:d,width:m,height:u}=l;if(c||e(),!m||!u)return;const h=it(d),g=it(i.clientWidth-(a+m)),p=it(i.clientHeight-(d+u)),w=it(a),v={rootMargin:-h+"px "+-g+"px "+-p+"px "+-w+"px",threshold:F(0,U(1,f))||1};let b=!0;function y(O){const R=O[0].intersectionRatio;if(R!==f){if(!b)return s();R?s(!1,R):o=setTimeout(()=>{s(!1,1e-7)},1e3)}R===1&&!Yt(l,t.getBoundingClientRect())&&s(),b=!1}try{n=new IntersectionObserver(y,{...v,root:i.ownerDocument})}catch{n=new IntersectionObserver(y,v)}n.observe(t)}return s(!0),r}function tn(t,e,n,o){o===void 0&&(o={});const{ancestorScroll:i=!0,ancestorResize:r=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:f=!1}=o,l=Rt(t),a=i||r?[...l?nt(l):[],...nt(e)]:[];a.forEach(w=>{i&&w.addEventListener("scroll",n,{passive:!0}),r&&w.addEventListener("resize",n)});const d=l&&c?He(l,n):null;let m=-1,u=null;s&&(u=new ResizeObserver(w=>{let[x]=w;x&&x.target===l&&u&&(u.unobserve(e),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var v;(v=u)==null||v.observe(e)})),n()}),l&&!f&&u.observe(l),u.observe(e));let h,g=f?G(t):null;f&&p();function p(){const w=G(t);g&&!Yt(g,w)&&n(),g=w,h=requestAnimationFrame(p)}return n(),()=>{var w;a.forEach(x=>{i&&x.removeEventListener("scroll",n),r&&x.removeEventListener("resize",n)}),d?.(),(w=u)==null||w.disconnect(),u=null,f&&cancelAnimationFrame(h)}}const _e=de,ze=me,je=fe,Ie=ge,Ye=ae,Ft=le,qe=he,Xe=(t,e,n)=>{const o=new Map,i={platform:Ve,...n},r={...i.platform,_c:o};return ce(t,e,{...i,platform:r})};var Ue=typeof document<"u",Ke=function(){},rt=Ue?S.useLayoutEffect:Ke;function ft(t,e){if(t===e)return!0;if(typeof t!=typeof e)return!1;if(typeof t=="function"&&t.toString()===e.toString())return!0;let n,o,i;if(t&&e&&typeof t=="object"){if(Array.isArray(t)){if(n=t.length,n!==e.length)return!1;for(o=n;o--!==0;)if(!ft(t[o],e[o]))return!1;return!0}if(i=Object.keys(t),n=i.length,n!==Object.keys(e).length)return!1;for(o=n;o--!==0;)if(!{}.hasOwnProperty.call(e,i[o]))return!1;for(o=n;o--!==0;){const r=i[o];if(!(r==="_owner"&&t.$$typeof)&&!ft(t[r],e[r]))return!1}return!0}return t!==t&&e!==e}function qt(t){return typeof window>"u"?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function $t(t,e){const n=qt(t);return Math.round(e*n)/n}function ht(t){const e=S.useRef(t);return rt(()=>{e.current=t}),e}function en(t){t===void 0&&(t={});const{placement:e="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:r,floating:s}={},transform:c=!0,whileElementsMounted:f,open:l}=t,[a,d]=S.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[m,u]=S.useState(o);ft(m,o)||u(o);const[h,g]=S.useState(null),[p,w]=S.useState(null),x=S.useCallback(A=>{A!==O.current&&(O.current=A,g(A))},[]),v=S.useCallback(A=>{A!==R.current&&(R.current=A,w(A))},[]),b=r||h,y=s||p,O=S.useRef(null),R=S.useRef(null),L=S.useRef(a),B=f!=null,P=ht(f),N=ht(i),M=ht(l),D=S.useCallback(()=>{if(!O.current||!R.current)return;const A={placement:e,strategy:n,middleware:m};N.current&&(A.platform=N.current),Xe(O.current,R.current,A).then(k=>{const X={...k,isPositioned:M.current!==!1};C.current&&!ft(L.current,X)&&(L.current=X,Ut.flushSync(()=>{d(X)}))})},[m,e,n,N,M]);rt(()=>{l===!1&&L.current.isPositioned&&(L.current.isPositioned=!1,d(A=>({...A,isPositioned:!1})))},[l]);const C=S.useRef(!1);rt(()=>(C.current=!0,()=>{C.current=!1}),[]),rt(()=>{if(b&&(O.current=b),y&&(R.current=y),b&&y){if(P.current)return P.current(b,y,D);D()}},[b,y,D,P,B]);const H=S.useMemo(()=>({reference:O,floating:R,setReference:x,setFloating:v}),[x,v]),E=S.useMemo(()=>({reference:b,floating:y}),[b,y]),T=S.useMemo(()=>{const A={position:n,left:0,top:0};if(!E.floating)return A;const k=$t(E.floating,a.x),X=$t(E.floating,a.y);return c?{...A,transform:"translate("+k+"px, "+X+"px)",...qt(E.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:k,top:X}},[n,c,E.floating,a.x,a.y]);return S.useMemo(()=>({...a,update:D,refs:H,elements:E,floatingStyles:T}),[a,D,H,E,T])}const Ge=t=>{function e(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:t,fn(n){const{element:o,padding:i}=typeof t=="function"?t(n):t;return o&&e(o)?o.current!=null?Ft({element:o.current,padding:i}).fn(n):{}:o?Ft({element:o,padding:i}).fn(n):{}}}},nn=(t,e)=>({..._e(t),options:[t,e]}),on=(t,e)=>({...ze(t),options:[t,e]}),rn=(t,e)=>({...qe(t),options:[t,e]}),sn=(t,e)=>({...je(t),options:[t,e]}),cn=(t,e)=>({...Ie(t),options:[t,e]}),ln=(t,e)=>({...Ye(t),options:[t,e]}),fn=(t,e)=>({...Ge(t),options:[t,e]});var Je="Arrow",Xt=S.forwardRef((t,e)=>{const{children:n,width:o=10,height:i=5,...r}=t;return Ct.jsx(Kt.svg,{...r,ref:e,width:o,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:t.asChild?n:Ct.jsx("polygon",{points:"0,0 30,0 15,10"})})});Xt.displayName=Je;var an=Xt;export{an as R,cn as a,fn as b,tn as c,sn as f,ln as h,rn as l,nn as o,on as s,en as u};
